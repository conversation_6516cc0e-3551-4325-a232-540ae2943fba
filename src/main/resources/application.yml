server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: auto-send-mail
  
  # 数据库配置
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
      
  # 邮件配置（默认配置，可在管理端动态配置）
  mail:
    default-encoding: UTF-8
    
# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: auto-send-mail-jwt-secret-key-2024
    expiration: 86400000 # 24小时
    
  # 文件上传配置
  upload:
    path: ./uploads/
    max-size: 52428800 # 50MB
    
  # 邮件发送配置
  mail:
    default-interval: 300 # 默认发送间隔（秒）
    max-retry: 3 # 最大重试次数
    
  # 管理员配置
  admin:
    username: admin
    password: admin123

# 日志配置
logging:
  level:
    com.automail: DEBUG
    org.springframework.mail: DEBUG
  file:
    name: ./logs/auto-send-mail.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
