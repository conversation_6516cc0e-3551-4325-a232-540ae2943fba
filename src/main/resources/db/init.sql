-- 创建数据库
CREATE DATABASE IF NOT EXISTS auto_send_mail DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE auto_send_mail;

-- 用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(50) COMMENT '真实姓名',
    role ENUM('ADMIN', 'USER') NOT NULL DEFAULT 'USER' COMMENT '角色',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) COMMENT '用户表';

-- 邮箱配置表
CREATE TABLE IF NOT EXISTS email_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    email_address VARCHAR(100) NOT NULL COMMENT '邮箱地址',
    protocol_type ENUM('SMTP', 'POP3', 'IMAP', 'EXCHANGE') NOT NULL COMMENT '协议类型',
    smtp_host VARCHAR(100) COMMENT 'SMTP服务器地址',
    smtp_port INT COMMENT 'SMTP端口',
    pop3_host VARCHAR(100) COMMENT 'POP3服务器地址',
    pop3_port INT COMMENT 'POP3端口',
    imap_host VARCHAR(100) COMMENT 'IMAP服务器地址',
    imap_port INT COMMENT 'IMAP端口',
    exchange_host VARCHAR(100) COMMENT 'Exchange服务器地址',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(200) NOT NULL COMMENT '密码/授权码',
    ssl_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用SSL',
    tls_enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用TLS',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    remark VARCHAR(500) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) COMMENT '邮箱配置表';

-- 收件人表
CREATE TABLE IF NOT EXISTS recipient (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '收件人姓名',
    email_address VARCHAR(100) NOT NULL COMMENT '收件人邮箱地址',
    group_name VARCHAR(50) COMMENT '分组名称',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    remark VARCHAR(500) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) COMMENT '收件人表';

-- 邮件发送计划表
CREATE TABLE IF NOT EXISTS mail_plan (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL COMMENT '计划名称',
    subject VARCHAR(200) NOT NULL COMMENT '邮件主题',
    content TEXT COMMENT '邮件内容',
    content_type ENUM('TEXT', 'HTML') NOT NULL DEFAULT 'HTML' COMMENT '内容类型',
    send_interval INT NOT NULL DEFAULT 300 COMMENT '发送间隔（秒）',
    start_time DATETIME COMMENT '计划开始时间',
    end_time DATETIME COMMENT '计划结束时间',
    status ENUM('DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'DRAFT' COMMENT '计划状态',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    remark VARCHAR(500) COMMENT '备注',
    sent_count INT NOT NULL DEFAULT 0 COMMENT '已投数量',
    pending_count INT NOT NULL DEFAULT 0 COMMENT '未投数量',
    auto_reply_count INT NOT NULL DEFAULT 0 COMMENT '自动回复数量',
    manual_reply_count INT NOT NULL DEFAULT 0 COMMENT '手工回复数量',
    last_sync_time DATETIME COMMENT '最后同步时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) COMMENT '邮件发送计划表';

-- 邮件发送记录表
CREATE TABLE IF NOT EXISTS mail_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    mail_plan_id BIGINT NOT NULL COMMENT '邮件计划ID',
    email_config_id BIGINT NOT NULL COMMENT '邮箱配置ID',
    recipient_id BIGINT NOT NULL COMMENT '收件人ID',
    subject VARCHAR(200) NOT NULL COMMENT '邮件主题',
    content TEXT COMMENT '邮件内容',
    send_status ENUM('PENDING', 'SENDING', 'SUCCESS', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '发送状态',
    send_time DATETIME COMMENT '发送时间',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    error_message VARCHAR(1000) COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (mail_plan_id) REFERENCES mail_plan(id),
    FOREIGN KEY (email_config_id) REFERENCES email_config(id),
    FOREIGN KEY (recipient_id) REFERENCES recipient(id)
) COMMENT '邮件发送记录表';

-- 附件表
CREATE TABLE IF NOT EXISTS attachment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    mail_plan_id BIGINT NOT NULL COMMENT '邮件计划ID',
    original_filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_filename VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(100) COMMENT '文件类型',
    file_md5 VARCHAR(32) COMMENT '文件MD5值',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    remark VARCHAR(500) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (mail_plan_id) REFERENCES mail_plan(id)
) COMMENT '附件表';

-- 插入默认管理员用户
INSERT INTO user (username, password, email, real_name, role, enabled) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '<EMAIL>', '系统管理员', 'ADMIN', TRUE)
ON DUPLICATE KEY UPDATE username = username;

-- 邮件同步记录表
CREATE TABLE IF NOT EXISTS mail_sync (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email_config_id BIGINT NOT NULL COMMENT '邮箱配置ID',
    mail_plan_id BIGINT COMMENT '邮件计划ID',
    message_id VARCHAR(500) COMMENT '邮件唯一标识',
    subject VARCHAR(500) COMMENT '邮件主题',
    sender VARCHAR(200) COMMENT '发件人',
    recipient VARCHAR(200) COMMENT '收件人',
    mail_type ENUM('SENT', 'RECEIVED') NOT NULL COMMENT '邮件类型',
    reply_type ENUM('NONE', 'AUTO_REPLY', 'MANUAL_REPLY') DEFAULT 'NONE' COMMENT '回复类型',
    is_auto_reply BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为自动回复',
    mail_time DATETIME COMMENT '邮件发送/接收时间',
    sync_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '同步时间',
    original_mail_id VARCHAR(100) COMMENT '原始邮件ID',
    content_summary VARCHAR(1000) COMMENT '邮件内容摘要',
    processed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已处理',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (email_config_id) REFERENCES email_config(id),
    FOREIGN KEY (mail_plan_id) REFERENCES mail_plan(id)
) COMMENT '邮件同步记录表';

-- 邮件同步统计表
CREATE TABLE IF NOT EXISTS mail_sync_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    email_config_id BIGINT NOT NULL COMMENT '邮箱配置ID',
    mail_plan_id BIGINT COMMENT '邮件计划ID',
    stats_date DATE NOT NULL COMMENT '统计日期',
    sent_count INT NOT NULL DEFAULT 0 COMMENT '已发送数量',
    received_count INT NOT NULL DEFAULT 0 COMMENT '已接收数量',
    auto_reply_count INT NOT NULL DEFAULT 0 COMMENT '自动回复数量',
    manual_reply_count INT NOT NULL DEFAULT 0 COMMENT '手工回复数量',
    total_reply_count INT NOT NULL DEFAULT 0 COMMENT '总回复数量',
    reply_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '回复率（百分比）',
    last_update_time DATETIME COMMENT '最后更新时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    FOREIGN KEY (email_config_id) REFERENCES email_config(id),
    FOREIGN KEY (mail_plan_id) REFERENCES mail_plan(id),
    UNIQUE KEY uk_email_plan_date (email_config_id, mail_plan_id, stats_date)
) COMMENT '邮件同步统计表';

-- 创建索引
CREATE INDEX idx_email_config_enabled ON email_config(enabled);
CREATE INDEX idx_recipient_enabled ON recipient(enabled);
CREATE INDEX idx_recipient_sort_order ON recipient(sort_order);
CREATE INDEX idx_mail_plan_status ON mail_plan(status);
CREATE INDEX idx_mail_plan_enabled ON mail_plan(enabled);
CREATE INDEX idx_mail_record_status ON mail_record(send_status);
CREATE INDEX idx_mail_record_send_time ON mail_record(send_time);
CREATE INDEX idx_attachment_enabled ON attachment(enabled);
CREATE INDEX idx_mail_sync_message_id ON mail_sync(message_id);
CREATE INDEX idx_mail_sync_mail_type ON mail_sync(mail_type);
CREATE INDEX idx_mail_sync_reply_type ON mail_sync(reply_type);
CREATE INDEX idx_mail_sync_mail_time ON mail_sync(mail_time);
CREATE INDEX idx_mail_sync_stats_date ON mail_sync_stats(stats_date);
CREATE INDEX idx_mail_sync_stats_email_plan ON mail_sync_stats(email_config_id, mail_plan_id);
