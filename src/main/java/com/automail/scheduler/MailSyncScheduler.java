package com.automail.scheduler;

import com.automail.service.MailSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 邮件同步定时任务
 */
@Slf4j
@Component
public class MailSyncScheduler {

    @Autowired
    private MailSyncService mailSyncService;

    /**
     * 每10分钟同步一次邮件数据
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    public void syncEmails() {
        try {
            log.info("开始执行邮件同步任务");
            mailSyncService.syncAllEmailConfigs();
            log.info("邮件同步任务执行完成");
        } catch (Exception e) {
            log.error("邮件同步任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时更新一次统计数据
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void updateStats() {
        try {
            log.info("开始更新邮件统计数据");
            mailSyncService.updateMailPlanStats();
            log.info("邮件统计数据更新完成");
        } catch (Exception e) {
            log.error("更新邮件统计数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每天凌晨2点执行一次全量同步
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void fullSync() {
        try {
            log.info("开始执行全量邮件同步");
            mailSyncService.syncAllEmailConfigs();
            mailSyncService.updateMailPlanStats();
            log.info("全量邮件同步完成");
        } catch (Exception e) {
            log.error("全量邮件同步失败: {}", e.getMessage(), e);
        }
    }
}
