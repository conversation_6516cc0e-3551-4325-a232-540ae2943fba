package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 收件人实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "recipient")
public class Recipient extends BaseEntity {

    /**
     * 收件人姓名
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 收件人邮箱地址
     */
    @Column(name = "email_address", nullable = false, length = 100)
    private String emailAddress;

    /**
     * 分组名称
     */
    @Column(name = "group_name", length = 50)
    private String groupName;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 排序序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
