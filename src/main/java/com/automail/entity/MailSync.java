package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 邮件同步记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mail_sync")
public class MailSync extends BaseEntity {

    /**
     * 关联的邮箱配置
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_config_id", nullable = false)
    private EmailConfig emailConfig;

    /**
     * 关联的邮件计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mail_plan_id")
    private MailPlan mailPlan;

    /**
     * 邮件唯一标识（Message-ID）
     */
    @Column(name = "message_id", length = 500)
    private String messageId;

    /**
     * 邮件主题
     */
    @Column(name = "subject", length = 500)
    private String subject;

    /**
     * 发件人
     */
    @Column(name = "sender", length = 200)
    private String sender;

    /**
     * 收件人
     */
    @Column(name = "recipient", length = 200)
    private String recipient;

    /**
     * 邮件类型：SENT, RECEIVED
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "mail_type", nullable = false)
    private MailType mailType;

    /**
     * 回复类型：NONE, AUTO_REPLY, MANUAL_REPLY
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "reply_type")
    private ReplyType replyType = ReplyType.NONE;

    /**
     * 是否为自动回复
     */
    @Column(name = "is_auto_reply", nullable = false)
    private Boolean isAutoReply = false;

    /**
     * 邮件发送/接收时间
     */
    @Column(name = "mail_time")
    private LocalDateTime mailTime;

    /**
     * 同步时间
     */
    @Column(name = "sync_time", nullable = false)
    private LocalDateTime syncTime;

    /**
     * 原始邮件ID（在邮件服务器中的ID）
     */
    @Column(name = "original_mail_id", length = 100)
    private String originalMailId;

    /**
     * 邮件内容摘要
     */
    @Column(name = "content_summary", length = 1000)
    private String contentSummary;

    /**
     * 是否已处理
     */
    @Column(name = "processed", nullable = false)
    private Boolean processed = false;

    /**
     * 邮件类型枚举
     */
    public enum MailType {
        SENT,     // 已发送
        RECEIVED  // 已接收
    }

    /**
     * 回复类型枚举
     */
    public enum ReplyType {
        NONE,         // 无回复
        AUTO_REPLY,   // 自动回复
        MANUAL_REPLY  // 手工回复
    }
}
