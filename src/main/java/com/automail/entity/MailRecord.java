package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 邮件发送记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mail_record")
public class MailRecord extends BaseEntity {

    /**
     * 关联的邮件计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mail_plan_id", nullable = false)
    private MailPlan mailPlan;

    /**
     * 关联的邮箱配置
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_config_id", nullable = false)
    private EmailConfig emailConfig;

    /**
     * 关联的收件人
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id", nullable = false)
    private Recipient recipient;

    /**
     * 邮件主题
     */
    @Column(name = "subject", nullable = false, length = 200)
    private String subject;

    /**
     * 邮件内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * 发送状态：PENDING, SENDING, SUCCESS, FAILED
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "send_status", nullable = false)
    private SendStatus sendStatus = SendStatus.PENDING;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    private LocalDateTime sendTime;

    /**
     * 重试次数
     */
    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        PENDING,  // 待发送
        SENDING,  // 发送中
        SUCCESS,  // 发送成功
        FAILED    // 发送失败
    }
}
