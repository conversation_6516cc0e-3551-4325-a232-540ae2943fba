package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "attachment")
public class Attachment extends BaseEntity {

    /**
     * 关联的邮件计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mail_plan_id", nullable = false)
    private MailPlan mailPlan;

    /**
     * 原始文件名
     */
    @Column(name = "original_filename", nullable = false, length = 255)
    private String originalFilename;

    /**
     * 存储文件名
     */
    @Column(name = "stored_filename", nullable = false, length = 255)
    private String storedFilename;

    /**
     * 文件路径
     */
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    /**
     * 文件类型
     */
    @Column(name = "content_type", length = 100)
    private String contentType;

    /**
     * 文件MD5值
     */
    @Column(name = "file_md5", length = 32)
    private String fileMd5;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
