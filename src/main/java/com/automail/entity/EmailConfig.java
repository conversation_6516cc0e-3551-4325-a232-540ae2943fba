package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邮箱配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "email_config")
public class EmailConfig extends BaseEntity {

    /**
     * 配置名称
     */
    @Column(name = "config_name", nullable = false, length = 100)
    private String configName;

    /**
     * 邮箱地址
     */
    @Column(name = "email_address", nullable = false, length = 100)
    private String emailAddress;

    /**
     * 邮件协议类型：SMTP, POP3, IMAP, EXCHANGE
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "protocol_type", nullable = false)
    private ProtocolType protocolType;

    /**
     * SMTP服务器地址
     */
    @Column(name = "smtp_host", length = 100)
    private String smtpHost;

    /**
     * SMTP端口
     */
    @Column(name = "smtp_port")
    private Integer smtpPort;

    /**
     * POP3服务器地址
     */
    @Column(name = "pop3_host", length = 100)
    private String pop3Host;

    /**
     * POP3端口
     */
    @Column(name = "pop3_port")
    private Integer pop3Port;

    /**
     * IMAP服务器地址
     */
    @Column(name = "imap_host", length = 100)
    private String imapHost;

    /**
     * IMAP端口
     */
    @Column(name = "imap_port")
    private Integer imapPort;

    /**
     * Exchange服务器地址
     */
    @Column(name = "exchange_host", length = 100)
    private String exchangeHost;

    /**
     * 用户名
     */
    @Column(name = "username", nullable = false, length = 100)
    private String username;

    /**
     * 密码/授权码
     */
    @Column(name = "password", nullable = false, length = 200)
    private String password;

    /**
     * 是否启用SSL
     */
    @Column(name = "ssl_enabled", nullable = false)
    private Boolean sslEnabled = true;

    /**
     * 是否启用TLS
     */
    @Column(name = "tls_enabled", nullable = false)
    private Boolean tlsEnabled = false;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 邮件协议类型枚举
     */
    public enum ProtocolType {
        SMTP, POP3, IMAP, EXCHANGE
    }
}
