package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 邮件发送计划实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mail_plan")
public class MailPlan extends BaseEntity {

    /**
     * 计划名称
     */
    @Column(name = "plan_name", nullable = false, length = 100)
    private String planName;

    /**
     * 邮件主题
     */
    @Column(name = "subject", nullable = false, length = 200)
    private String subject;

    /**
     * 邮件内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * 邮件内容类型：TEXT, HTML
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "content_type", nullable = false)
    private ContentType contentType = ContentType.HTML;

    /**
     * 发送间隔（秒）
     */
    @Column(name = "send_interval", nullable = false)
    private Integer sendInterval = 300;

    /**
     * 计划开始时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 计划结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 计划状态：DRAFT, ACTIVE, PAUSED, COMPLETED, CANCELLED
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PlanStatus status = PlanStatus.DRAFT;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 邮件内容类型枚举
     */
    public enum ContentType {
        TEXT, HTML
    }

    /**
     * 计划状态枚举
     */
    public enum PlanStatus {
        DRAFT,      // 草稿
        ACTIVE,     // 活跃
        PAUSED,     // 暂停
        COMPLETED,  // 完成
        CANCELLED   // 取消
    }
}
