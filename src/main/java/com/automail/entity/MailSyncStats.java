package com.automail.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 邮件同步统计实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mail_sync_stats")
public class MailSyncStats extends BaseEntity {

    /**
     * 关联的邮箱配置
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_config_id", nullable = false)
    private EmailConfig emailConfig;

    /**
     * 关联的邮件计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mail_plan_id")
    private MailPlan mailPlan;

    /**
     * 统计日期
     */
    @Column(name = "stats_date", nullable = false)
    private LocalDate statsDate;

    /**
     * 已发送数量
     */
    @Column(name = "sent_count", nullable = false)
    private Integer sentCount = 0;

    /**
     * 已接收数量
     */
    @Column(name = "received_count", nullable = false)
    private Integer receivedCount = 0;

    /**
     * 自动回复数量
     */
    @Column(name = "auto_reply_count", nullable = false)
    private Integer autoReplyCount = 0;

    /**
     * 手工回复数量
     */
    @Column(name = "manual_reply_count", nullable = false)
    private Integer manualReplyCount = 0;

    /**
     * 总回复数量
     */
    @Column(name = "total_reply_count", nullable = false)
    private Integer totalReplyCount = 0;

    /**
     * 回复率（百分比）
     */
    @Column(name = "reply_rate")
    private Double replyRate = 0.0;

    /**
     * 最后更新时间
     */
    @Column(name = "last_update_time")
    private LocalDateTime lastUpdateTime;
}
