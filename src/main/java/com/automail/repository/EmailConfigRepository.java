package com.automail.repository;

import com.automail.entity.EmailConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 邮箱配置数据访问层
 */
@Repository
public interface EmailConfigRepository extends JpaRepository<EmailConfig, Long> {

    /**
     * 查找所有启用的邮箱配置
     */
    @Query("SELECT e FROM EmailConfig e WHERE e.enabled = true AND e.deleted = false ORDER BY e.id")
    List<EmailConfig> findAllEnabled();

    /**
     * 根据邮箱地址查找配置
     */
    @Query("SELECT e FROM EmailConfig e WHERE e.emailAddress = ?1 AND e.deleted = false")
    List<EmailConfig> findByEmailAddress(String emailAddress);

    /**
     * 查找所有未删除的配置
     */
    @Query("SELECT e FROM EmailConfig e WHERE e.deleted = false ORDER BY e.id DESC")
    List<EmailConfig> findAllNotDeleted();
}
