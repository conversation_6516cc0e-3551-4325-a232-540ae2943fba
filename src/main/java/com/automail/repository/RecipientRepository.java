package com.automail.repository;

import com.automail.entity.Recipient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 收件人数据访问层
 */
@Repository
public interface RecipientRepository extends JpaRepository<Recipient, Long> {

    /**
     * 查找所有启用的收件人，按排序序号排序
     */
    @Query("SELECT r FROM Recipient r WHERE r.enabled = true AND r.deleted = false ORDER BY r.sortOrder, r.id")
    List<Recipient> findAllEnabledOrderBySortOrder();

    /**
     * 根据分组查找收件人
     */
    @Query("SELECT r FROM Recipient r WHERE r.groupName = ?1 AND r.enabled = true AND r.deleted = false ORDER BY r.sortOrder, r.id")
    List<Recipient> findByGroupNameAndEnabled(String groupName);

    /**
     * 查找所有未删除的收件人
     */
    @Query("SELECT r FROM Recipient r WHERE r.deleted = false ORDER BY r.sortOrder, r.id")
    List<Recipient> findAllNotDeleted();

    /**
     * 根据邮箱地址查找收件人
     */
    @Query("SELECT r FROM Recipient r WHERE r.emailAddress = ?1 AND r.deleted = false")
    List<Recipient> findByEmailAddress(String emailAddress);
}
