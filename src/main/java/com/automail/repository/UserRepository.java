package com.automail.repository;

import com.automail.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = ?1 AND u.deleted = false")
    Optional<User> findByUsername(String username);

    /**
     * 根据用户名和启用状态查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = ?1 AND u.enabled = ?2 AND u.deleted = false")
    Optional<User> findByUsernameAndEnabled(String username, Boolean enabled);
}
