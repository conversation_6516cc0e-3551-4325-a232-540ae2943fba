package com.automail.repository;

import com.automail.entity.MailPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 邮件计划数据访问层
 */
@Repository
public interface MailPlanRepository extends JpaRepository<MailPlan, Long> {

    /**
     * 查找所有活跃的邮件计划
     */
    @Query("SELECT m FROM MailPlan m WHERE m.status = 'ACTIVE' AND m.enabled = true AND m.deleted = false")
    List<MailPlan> findAllActive();

    /**
     * 查找所有未删除的邮件计划
     */
    @Query("SELECT m FROM MailPlan m WHERE m.deleted = false ORDER BY m.id DESC")
    List<MailPlan> findAllNotDeleted();

    /**
     * 根据状态查找邮件计划
     */
    @Query("SELECT m FROM MailPlan m WHERE m.status = ?1 AND m.deleted = false ORDER BY m.id DESC")
    List<MailPlan> findByStatus(MailPlan.PlanStatus status);
}
