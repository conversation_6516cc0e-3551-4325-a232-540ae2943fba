package com.automail.repository;

import com.automail.entity.MailSync;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邮件同步记录数据访问层
 */
@Repository
public interface MailSyncRepository extends JpaRepository<MailSync, Long> {

    /**
     * 根据Message-ID查找邮件同步记录
     */
    @Query("SELECT m FROM MailSync m WHERE m.messageId = ?1 AND m.deleted = false")
    Optional<MailSync> findByMessageId(String messageId);

    /**
     * 根据邮箱配置ID查找同步记录
     */
    @Query("SELECT m FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.deleted = false ORDER BY m.mailTime DESC")
    List<MailSync> findByEmailConfigId(Long emailConfigId);

    /**
     * 根据邮件计划ID查找同步记录
     */
    @Query("SELECT m FROM MailSync m WHERE m.mailPlan.id = ?1 AND m.deleted = false ORDER BY m.mailTime DESC")
    List<MailSync> findByMailPlanId(Long mailPlanId);

    /**
     * 查找指定时间范围内的已发送邮件
     */
    @Query("SELECT m FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.mailType = 'SENT' AND m.mailTime BETWEEN ?2 AND ?3 AND m.deleted = false")
    List<MailSync> findSentMailsByEmailConfigAndTimeRange(Long emailConfigId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找指定时间范围内的已接收邮件
     */
    @Query("SELECT m FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.mailType = 'RECEIVED' AND m.mailTime BETWEEN ?2 AND ?3 AND m.deleted = false")
    List<MailSync> findReceivedMailsByEmailConfigAndTimeRange(Long emailConfigId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找自动回复邮件
     */
    @Query("SELECT m FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.replyType = 'AUTO_REPLY' AND m.mailTime BETWEEN ?2 AND ?3 AND m.deleted = false")
    List<MailSync> findAutoRepliesByEmailConfigAndTimeRange(Long emailConfigId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找手工回复邮件
     */
    @Query("SELECT m FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.replyType = 'MANUAL_REPLY' AND m.mailTime BETWEEN ?2 AND ?3 AND m.deleted = false")
    List<MailSync> findManualRepliesByEmailConfigAndTimeRange(Long emailConfigId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定邮箱配置的已发送邮件数量
     */
    @Query("SELECT COUNT(m) FROM MailSync m WHERE m.emailConfig.id = ?1 AND m.mailType = 'SENT' AND m.deleted = false")
    Long countSentMailsByEmailConfig(Long emailConfigId);

    /**
     * 统计指定邮件计划的已发送邮件数量
     */
    @Query("SELECT COUNT(m) FROM MailSync m WHERE m.mailPlan.id = ?1 AND m.mailType = 'SENT' AND m.deleted = false")
    Long countSentMailsByMailPlan(Long mailPlanId);

    /**
     * 统计指定邮件计划的自动回复数量
     */
    @Query("SELECT COUNT(m) FROM MailSync m WHERE m.mailPlan.id = ?1 AND m.replyType = 'AUTO_REPLY' AND m.deleted = false")
    Long countAutoRepliesByMailPlan(Long mailPlanId);

    /**
     * 统计指定邮件计划的手工回复数量
     */
    @Query("SELECT COUNT(m) FROM MailSync m WHERE m.mailPlan.id = ?1 AND m.replyType = 'MANUAL_REPLY' AND m.deleted = false")
    Long countManualRepliesByMailPlan(Long mailPlanId);

    /**
     * 查找未处理的同步记录
     */
    @Query("SELECT m FROM MailSync m WHERE m.processed = false AND m.deleted = false ORDER BY m.syncTime")
    List<MailSync> findUnprocessedRecords();
}
