package com.automail.repository;

import com.automail.entity.MailSyncStats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 邮件同步统计数据访问层
 */
@Repository
public interface MailSyncStatsRepository extends JpaRepository<MailSyncStats, Long> {

    /**
     * 根据邮箱配置、邮件计划和日期查找统计记录
     */
    @Query("SELECT m FROM MailSyncStats m WHERE m.emailConfig.id = ?1 AND m.mailPlan.id = ?2 AND m.statsDate = ?3 AND m.deleted = false")
    Optional<MailSyncStats> findByEmailConfigAndMailPlanAndDate(Long emailConfigId, Long mailPlanId, LocalDate statsDate);

    /**
     * 根据邮件计划查找统计记录
     */
    @Query("SELECT m FROM MailSyncStats m WHERE m.mailPlan.id = ?1 AND m.deleted = false ORDER BY m.statsDate DESC")
    List<MailSyncStats> findByMailPlanId(Long mailPlanId);

    /**
     * 根据邮箱配置查找统计记录
     */
    @Query("SELECT m FROM MailSyncStats m WHERE m.emailConfig.id = ?1 AND m.deleted = false ORDER BY m.statsDate DESC")
    List<MailSyncStats> findByEmailConfigId(Long emailConfigId);

    /**
     * 查找指定日期范围内的统计记录
     */
    @Query("SELECT m FROM MailSyncStats m WHERE m.mailPlan.id = ?1 AND m.statsDate BETWEEN ?2 AND ?3 AND m.deleted = false ORDER BY m.statsDate DESC")
    List<MailSyncStats> findByMailPlanIdAndDateRange(Long mailPlanId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取邮件计划的最新统计记录
     */
    @Query("SELECT m FROM MailSyncStats m WHERE m.mailPlan.id = ?1 AND m.deleted = false ORDER BY m.statsDate DESC LIMIT 1")
    Optional<MailSyncStats> findLatestByMailPlanId(Long mailPlanId);

    /**
     * 汇总邮件计划的总统计数据
     */
    @Query("SELECT " +
           "SUM(m.sentCount) as totalSent, " +
           "SUM(m.receivedCount) as totalReceived, " +
           "SUM(m.autoReplyCount) as totalAutoReply, " +
           "SUM(m.manualReplyCount) as totalManualReply, " +
           "SUM(m.totalReplyCount) as totalReply " +
           "FROM MailSyncStats m WHERE m.mailPlan.id = ?1 AND m.deleted = false")
    Object[] sumStatsByMailPlanId(Long mailPlanId);
}
