package com.automail.repository;

import com.automail.entity.MailRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮件发送记录数据访问层
 */
@Repository
public interface MailRecordRepository extends JpaRepository<MailRecord, Long> {

    /**
     * 查找待发送的邮件记录
     */
    @Query("SELECT m FROM MailRecord m WHERE m.sendStatus = 'PENDING' AND m.deleted = false ORDER BY m.id")
    List<MailRecord> findPendingRecords();

    /**
     * 查找失败且可重试的邮件记录
     */
    @Query("SELECT m FROM MailRecord m WHERE m.sendStatus = 'FAILED' AND m.retryCount < ?1 AND m.deleted = false ORDER BY m.id")
    List<MailRecord> findFailedRecordsForRetry(int maxRetryCount);

    /**
     * 根据邮件计划ID查找记录
     */
    @Query("SELECT m FROM MailRecord m WHERE m.mailPlan.id = ?1 AND m.deleted = false ORDER BY m.id DESC")
    Page<MailRecord> findByMailPlanId(Long mailPlanId, Pageable pageable);

    /**
     * 根据发送状态查找记录
     */
    @Query("SELECT m FROM MailRecord m WHERE m.sendStatus = ?1 AND m.deleted = false ORDER BY m.id DESC")
    Page<MailRecord> findBySendStatus(MailRecord.SendStatus sendStatus, Pageable pageable);

    /**
     * 统计指定时间范围内的发送记录
     */
    @Query("SELECT COUNT(m) FROM MailRecord m WHERE m.sendTime BETWEEN ?1 AND ?2 AND m.sendStatus = ?3 AND m.deleted = false")
    Long countBySendTimeBetweenAndSendStatus(LocalDateTime startTime, LocalDateTime endTime, MailRecord.SendStatus sendStatus);

    /**
     * 查找所有未删除的记录
     */
    @Query("SELECT m FROM MailRecord m WHERE m.deleted = false ORDER BY m.id DESC")
    Page<MailRecord> findAllNotDeleted(Pageable pageable);
}
