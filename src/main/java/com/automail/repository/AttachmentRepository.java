package com.automail.repository;

import com.automail.entity.Attachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 附件数据访问层
 */
@Repository
public interface AttachmentRepository extends JpaRepository<Attachment, Long> {

    /**
     * 根据邮件计划ID查找启用的附件
     */
    @Query("SELECT a FROM Attachment a WHERE a.mailPlan.id = ?1 AND a.enabled = true AND a.deleted = false ORDER BY a.id")
    List<Attachment> findByMailPlanIdAndEnabled(Long mailPlanId);

    /**
     * 根据邮件计划ID查找所有附件
     */
    @Query("SELECT a FROM Attachment a WHERE a.mailPlan.id = ?1 AND a.deleted = false ORDER BY a.id")
    List<Attachment> findByMailPlanId(Long mailPlanId);

    /**
     * 查找所有未删除的附件
     */
    @Query("SELECT a FROM Attachment a WHERE a.deleted = false ORDER BY a.id DESC")
    List<Attachment> findAllNotDeleted();
}
