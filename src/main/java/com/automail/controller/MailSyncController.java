package com.automail.controller;

import com.automail.dto.Result;
import com.automail.service.MailSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 邮件同步控制器
 */
@RestController
@RequestMapping("/api/mail-sync")
public class MailSyncController {

    @Autowired
    private MailSyncService mailSyncService;

    /**
     * 同步所有邮箱配置
     */
    @PostMapping("/sync-all")
    public Result<Void> syncAllEmailConfigs() {
        try {
            mailSyncService.syncAllEmailConfigs();
            return Result.success("同步成功");
        } catch (Exception e) {
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 同步指定邮箱配置
     */
    @PostMapping("/sync/{emailConfigId}")
    public Result<Void> syncEmailConfig(@PathVariable Long emailConfigId) {
        try {
            mailSyncService.syncEmailConfig(emailConfigId);
            return Result.success("同步成功");
        } catch (Exception e) {
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 更新统计数据
     */
    @PostMapping("/update-stats")
    public Result<Void> updateStats() {
        try {
            mailSyncService.updateMailPlanStats();
            return Result.success("统计数据更新成功");
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }
}
