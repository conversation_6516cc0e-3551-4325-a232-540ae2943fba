package com.automail.controller;

import com.automail.dto.LoginRequest;
import com.automail.dto.LoginResponse;
import com.automail.dto.Result;
import com.automail.entity.User;
import com.automail.service.UserService;
import com.automail.utils.JwtUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtils jwtUtils;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        Optional<User> userOpt = userService.findByUsername(loginRequest.getUsername());
        
        if (userOpt.isEmpty()) {
            return Result.error("用户名或密码错误");
        }

        User user = userOpt.get();
        
        if (!user.getEnabled()) {
            return Result.error("用户已被禁用");
        }

        if (!userService.validatePassword(loginRequest.getPassword(), user.getPassword())) {
            return Result.error("用户名或密码错误");
        }

        // 生成JWT Token
        String token = jwtUtils.generateToken(user.getUsername());

        LoginResponse response = new LoginResponse(
                token,
                user.getUsername(),
                user.getEmail(),
                user.getRealName(),
                user.getRole().name()
        );

        return Result.success("登录成功", response);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，客户端删除token即可
        return Result.success();
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public Result<User> getCurrentUser(@RequestHeader("Authorization") String token) {
        try {
            String jwt = token.substring(7); // 移除 "Bearer " 前缀
            String username = jwtUtils.getUsernameFromToken(jwt);
            
            Optional<User> userOpt = userService.findByUsername(username);
            if (userOpt.isEmpty()) {
                return Result.error("用户不存在");
            }

            User user = userOpt.get();
            // 不返回密码
            user.setPassword(null);
            
            return Result.success(user);
        } catch (Exception e) {
            return Result.error("获取用户信息失败");
        }
    }
}
