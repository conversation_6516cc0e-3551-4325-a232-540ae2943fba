package com.automail.controller;

import com.automail.dto.PageResult;
import com.automail.dto.Result;
import com.automail.entity.MailPlan;
import com.automail.entity.MailRecord;
import com.automail.entity.MailSyncStats;
import com.automail.service.MailPlanService;
import com.automail.service.MailSyncService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 邮件计划控制器
 */
@RestController
@RequestMapping("/api/mail-plans")
public class MailPlanController {

    @Autowired
    private MailPlanService mailPlanService;

    @Autowired
    private MailSyncService mailSyncService;

    /**
     * 获取邮件计划列表（分页）
     */
    @GetMapping
    public Result<PageResult<MailPlan>> getMailPlans(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(current - 1, size, sort);
        
        Page<MailPlan> page = mailPlanService.getMailPlans(pageable);
        PageResult<MailPlan> result = new PageResult<>(page);
        
        return Result.success(result);
    }

    /**
     * 获取所有邮件计划（不分页）
     */
    @GetMapping("/all")
    public Result<List<MailPlan>> getAllMailPlans() {
        List<MailPlan> mailPlans = mailPlanService.getAllMailPlans();
        return Result.success(mailPlans);
    }

    /**
     * 根据ID获取邮件计划
     */
    @GetMapping("/{id}")
    public Result<MailPlan> getMailPlan(@PathVariable Long id) {
        Optional<MailPlan> mailPlan = mailPlanService.getMailPlanById(id);
        if (mailPlan.isPresent()) {
            return Result.success(mailPlan.get());
        } else {
            return Result.notFound();
        }
    }

    /**
     * 创建邮件计划
     */
    @PostMapping
    public Result<MailPlan> createMailPlan(@Valid @RequestBody MailPlan mailPlan) {
        try {
            MailPlan created = mailPlanService.createMailPlan(mailPlan);
            return Result.success("创建成功", created);
        } catch (Exception e) {
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新邮件计划
     */
    @PutMapping("/{id}")
    public Result<MailPlan> updateMailPlan(@PathVariable Long id, @Valid @RequestBody MailPlan mailPlan) {
        try {
            mailPlan.setId(id);
            MailPlan updated = mailPlanService.updateMailPlan(mailPlan);
            return Result.success("更新成功", updated);
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除邮件计划
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteMailPlan(@PathVariable Long id) {
        try {
            mailPlanService.deleteMailPlan(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 启动邮件计划
     */
    @PostMapping("/{id}/start")
    public Result<Void> startMailPlan(@PathVariable Long id) {
        try {
            mailPlanService.startMailPlan(id);
            return Result.success("启动成功");
        } catch (Exception e) {
            return Result.error("启动失败: " + e.getMessage());
        }
    }

    /**
     * 暂停邮件计划
     */
    @PostMapping("/{id}/pause")
    public Result<Void> pauseMailPlan(@PathVariable Long id) {
        try {
            mailPlanService.pauseMailPlan(id);
            return Result.success("暂停成功");
        } catch (Exception e) {
            return Result.error("暂停失败: " + e.getMessage());
        }
    }

    /**
     * 停止邮件计划
     */
    @PostMapping("/{id}/stop")
    public Result<Void> stopMailPlan(@PathVariable Long id) {
        try {
            mailPlanService.stopMailPlan(id);
            return Result.success("停止成功");
        } catch (Exception e) {
            return Result.error("停止失败: " + e.getMessage());
        }
    }

    /**
     * 刷新邮件计划统计数据
     */
    @PostMapping("/{id}/refresh-stats")
    public Result<Void> refreshMailPlanStats(@PathVariable Long id) {
        try {
            mailPlanService.refreshMailPlanStats(id);
            return Result.success("统计数据刷新成功");
        } catch (Exception e) {
            return Result.error("刷新失败: " + e.getMessage());
        }
    }

    /**
     * 同步邮件数据
     */
    @PostMapping("/{id}/sync")
    public Result<Void> syncMailPlan(@PathVariable Long id) {
        try {
            // 这里需要根据邮件计划关联的邮箱配置进行同步
            mailSyncService.syncAllEmailConfigs();
            return Result.success("同步成功");
        } catch (Exception e) {
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取邮件计划的发送记录
     */
    @GetMapping("/{id}/records")
    public Result<PageResult<MailRecord>> getMailPlanRecords(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Pageable pageable = PageRequest.of(current - 1, size, Sort.by(Sort.Direction.DESC, "id"));
        Page<MailRecord> page = mailPlanService.getMailPlanRecords(id, pageable);
        PageResult<MailRecord> result = new PageResult<>(page);
        
        return Result.success(result);
    }

    /**
     * 获取邮件计划的统计历史
     */
    @GetMapping("/{id}/stats")
    public Result<List<MailSyncStats>> getMailPlanStats(@PathVariable Long id) {
        List<MailSyncStats> stats = mailPlanService.getMailPlanStatsHistory(id);
        return Result.success(stats);
    }

    /**
     * 获取邮件计划的统计历史（指定日期范围）
     */
    @GetMapping("/{id}/stats/range")
    public Result<List<MailSyncStats>> getMailPlanStatsRange(
            @PathVariable Long id,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<MailSyncStats> stats = mailPlanService.getMailPlanStatsHistory(id, startDate, endDate);
        return Result.success(stats);
    }

    /**
     * 获取邮件计划的汇总统计
     */
    @GetMapping("/{id}/summary")
    public Result<MailPlanService.MailPlanSummaryStats> getMailPlanSummary(@PathVariable Long id) {
        try {
            MailPlanService.MailPlanSummaryStats summary = mailPlanService.getMailPlanSummaryStats(id);
            return Result.success(summary);
        } catch (Exception e) {
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃的邮件计划
     */
    @GetMapping("/active")
    public Result<List<MailPlan>> getActiveMailPlans() {
        List<MailPlan> activeMailPlans = mailPlanService.getActiveMailPlans();
        return Result.success(activeMailPlans);
    }
}
