package com.automail.service;

import com.automail.entity.*;
import com.automail.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.search.ComparisonTerm;
import javax.mail.search.ReceivedDateTerm;
import javax.mail.search.SearchTerm;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 邮件同步服务
 */
@Slf4j
@Service
public class MailSyncService {

    @Autowired
    private MailSyncRepository mailSyncRepository;

    @Autowired
    private MailSyncStatsRepository mailSyncStatsRepository;

    @Autowired
    private EmailConfigRepository emailConfigRepository;

    @Autowired
    private MailPlanRepository mailPlanRepository;

    @Autowired
    private MailRecordRepository mailRecordRepository;

    // 自动回复关键词模式
    private static final List<Pattern> AUTO_REPLY_PATTERNS = Arrays.asList(
        Pattern.compile(".*自动回复.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*auto.*reply.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*out.*of.*office.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*vacation.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*away.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*不在办公室.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*休假.*", Pattern.CASE_INSENSITIVE)
    );

    /**
     * 同步指定邮箱配置的邮件
     */
    @Transactional
    public void syncEmailConfig(Long emailConfigId) {
        try {
            EmailConfig emailConfig = emailConfigRepository.findById(emailConfigId)
                    .orElseThrow(() -> new RuntimeException("邮箱配置不存在"));

            log.info("开始同步邮箱: {}", emailConfig.getEmailAddress());

            // 同步已发送邮件
            syncSentMails(emailConfig);

            // 同步已接收邮件
            syncReceivedMails(emailConfig);

            // 更新统计数据
            updateMailPlanStats();

            log.info("邮箱同步完成: {}", emailConfig.getEmailAddress());

        } catch (Exception e) {
            log.error("同步邮箱失败: {}", e.getMessage(), e);
            throw new RuntimeException("同步邮箱失败: " + e.getMessage());
        }
    }

    /**
     * 同步所有启用的邮箱配置
     */
    public void syncAllEmailConfigs() {
        List<EmailConfig> emailConfigs = emailConfigRepository.findAllEnabled();
        for (EmailConfig emailConfig : emailConfigs) {
            try {
                syncEmailConfig(emailConfig.getId());
            } catch (Exception e) {
                log.error("同步邮箱失败: {}, 错误: {}", emailConfig.getEmailAddress(), e.getMessage());
            }
        }
    }

    /**
     * 同步已发送邮件
     */
    private void syncSentMails(EmailConfig emailConfig) throws MessagingException {
        if (emailConfig.getSmtpHost() == null) {
            log.warn("邮箱配置缺少SMTP信息，跳过已发送邮件同步: {}", emailConfig.getEmailAddress());
            return;
        }

        Properties props = new Properties();
        props.put("mail.store.protocol", "imaps");
        props.put("mail.imaps.host", emailConfig.getImapHost() != null ? emailConfig.getImapHost() : emailConfig.getSmtpHost());
        props.put("mail.imaps.port", emailConfig.getImapPort() != null ? emailConfig.getImapPort() : 993);
        props.put("mail.imaps.ssl.enable", emailConfig.getSslEnabled());

        Session session = Session.getInstance(props);
        Store store = session.getStore("imaps");
        store.connect(emailConfig.getUsername(), emailConfig.getPassword());

        try {
            // 获取已发送文件夹
            Folder sentFolder = getSentFolder(store);
            if (sentFolder != null) {
                sentFolder.open(Folder.READ_ONLY);
                
                // 获取最近7天的邮件
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -7);
                Date since = cal.getTime();
                
                SearchTerm searchTerm = new ReceivedDateTerm(ComparisonTerm.GE, since);
                Message[] messages = sentFolder.search(searchTerm);

                for (Message message : messages) {
                    processSentMessage(emailConfig, message);
                }

                sentFolder.close(false);
            }
        } finally {
            store.close();
        }
    }

    /**
     * 同步已接收邮件
     */
    private void syncReceivedMails(EmailConfig emailConfig) throws MessagingException {
        Properties props = new Properties();
        props.put("mail.store.protocol", "imaps");
        props.put("mail.imaps.host", emailConfig.getImapHost() != null ? emailConfig.getImapHost() : emailConfig.getSmtpHost());
        props.put("mail.imaps.port", emailConfig.getImapPort() != null ? emailConfig.getImapPort() : 993);
        props.put("mail.imaps.ssl.enable", emailConfig.getSslEnabled());

        Session session = Session.getInstance(props);
        Store store = session.getStore("imaps");
        store.connect(emailConfig.getUsername(), emailConfig.getPassword());

        try {
            Folder inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);

            // 获取最近7天的邮件
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -7);
            Date since = cal.getTime();
            
            SearchTerm searchTerm = new ReceivedDateTerm(ComparisonTerm.GE, since);
            Message[] messages = inbox.search(searchTerm);

            for (Message message : messages) {
                processReceivedMessage(emailConfig, message);
            }

            inbox.close(false);
        } finally {
            store.close();
        }
    }

    /**
     * 获取已发送文件夹
     */
    private Folder getSentFolder(Store store) throws MessagingException {
        String[] sentFolderNames = {"Sent", "Sent Items", "已发送", "发件箱"};
        
        for (String folderName : sentFolderNames) {
            try {
                Folder folder = store.getFolder(folderName);
                if (folder.exists()) {
                    return folder;
                }
            } catch (MessagingException e) {
                // 继续尝试下一个文件夹名
            }
        }
        
        return null;
    }

    /**
     * 处理已发送邮件
     */
    private void processSentMessage(EmailConfig emailConfig, Message message) {
        try {
            String messageId = getMessageId(message);
            if (messageId == null || mailSyncRepository.findByMessageId(messageId).isPresent()) {
                return; // 已存在，跳过
            }

            MailSync mailSync = new MailSync();
            mailSync.setEmailConfig(emailConfig);
            mailSync.setMessageId(messageId);
            mailSync.setSubject(message.getSubject());
            mailSync.setSender(emailConfig.getEmailAddress());
            mailSync.setRecipient(getRecipientAddress(message));
            mailSync.setMailType(MailSync.MailType.SENT);
            mailSync.setReplyType(MailSync.ReplyType.NONE);
            mailSync.setIsAutoReply(false);
            mailSync.setMailTime(convertToLocalDateTime(message.getSentDate()));
            mailSync.setSyncTime(LocalDateTime.now());
            mailSync.setContentSummary(getContentSummary(message));

            // 尝试关联邮件计划
            MailPlan mailPlan = findRelatedMailPlan(message.getSubject());
            if (mailPlan != null) {
                mailSync.setMailPlan(mailPlan);
            }

            mailSyncRepository.save(mailSync);

        } catch (Exception e) {
            log.error("处理已发送邮件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理已接收邮件
     */
    private void processReceivedMessage(EmailConfig emailConfig, Message message) {
        try {
            String messageId = getMessageId(message);
            if (messageId == null || mailSyncRepository.findByMessageId(messageId).isPresent()) {
                return; // 已存在，跳过
            }

            MailSync mailSync = new MailSync();
            mailSync.setEmailConfig(emailConfig);
            mailSync.setMessageId(messageId);
            mailSync.setSubject(message.getSubject());
            mailSync.setSender(getSenderAddress(message));
            mailSync.setRecipient(emailConfig.getEmailAddress());
            mailSync.setMailType(MailSync.MailType.RECEIVED);
            
            // 判断是否为自动回复
            boolean isAutoReply = isAutoReply(message);
            mailSync.setIsAutoReply(isAutoReply);
            mailSync.setReplyType(isAutoReply ? MailSync.ReplyType.AUTO_REPLY : MailSync.ReplyType.MANUAL_REPLY);
            
            mailSync.setMailTime(convertToLocalDateTime(message.getReceivedDate()));
            mailSync.setSyncTime(LocalDateTime.now());
            mailSync.setContentSummary(getContentSummary(message));

            // 尝试关联邮件计划
            MailPlan mailPlan = findRelatedMailPlan(message.getSubject());
            if (mailPlan != null) {
                mailSync.setMailPlan(mailPlan);
            }

            mailSyncRepository.save(mailSync);

        } catch (Exception e) {
            log.error("处理已接收邮件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新邮件计划统计数据
     */
    @Transactional
    public void updateMailPlanStats() {
        List<MailPlan> mailPlans = mailPlanRepository.findAllNotDeleted();
        
        for (MailPlan mailPlan : mailPlans) {
            try {
                // 统计已发送数量
                Long sentCount = mailSyncRepository.countSentMailsByMailPlan(mailPlan.getId());
                
                // 统计待发送数量
                Long pendingCount = mailRecordRepository.findPendingRecords().stream()
                        .filter(record -> record.getMailPlan().getId().equals(mailPlan.getId()))
                        .count();
                
                // 统计自动回复数量
                Long autoReplyCount = mailSyncRepository.countAutoRepliesByMailPlan(mailPlan.getId());
                
                // 统计手工回复数量
                Long manualReplyCount = mailSyncRepository.countManualRepliesByMailPlan(mailPlan.getId());

                // 更新邮件计划统计
                mailPlan.setSentCount(sentCount.intValue());
                mailPlan.setPendingCount(pendingCount.intValue());
                mailPlan.setAutoReplyCount(autoReplyCount.intValue());
                mailPlan.setManualReplyCount(manualReplyCount.intValue());
                mailPlan.setLastSyncTime(LocalDateTime.now());

                mailPlanRepository.save(mailPlan);

                // 更新每日统计
                updateDailyStats(mailPlan);

            } catch (Exception e) {
                log.error("更新邮件计划统计失败: {}, 错误: {}", mailPlan.getPlanName(), e.getMessage());
            }
        }
    }

    /**
     * 更新每日统计数据
     */
    private void updateDailyStats(MailPlan mailPlan) {
        LocalDate today = LocalDate.now();
        
        // 查找或创建今日统计记录
        Optional<MailSyncStats> statsOpt = mailSyncStatsRepository
                .findByEmailConfigAndMailPlanAndDate(null, mailPlan.getId(), today);
        
        MailSyncStats stats = statsOpt.orElse(new MailSyncStats());
        if (statsOpt.isEmpty()) {
            stats.setMailPlan(mailPlan);
            stats.setStatsDate(today);
        }

        stats.setSentCount(mailPlan.getSentCount());
        stats.setAutoReplyCount(mailPlan.getAutoReplyCount());
        stats.setManualReplyCount(mailPlan.getManualReplyCount());
        stats.setTotalReplyCount(mailPlan.getAutoReplyCount() + mailPlan.getManualReplyCount());
        
        // 计算回复率
        if (mailPlan.getSentCount() > 0) {
            double replyRate = (double) stats.getTotalReplyCount() / mailPlan.getSentCount() * 100;
            stats.setReplyRate(replyRate);
        }
        
        stats.setLastUpdateTime(LocalDateTime.now());
        
        mailSyncStatsRepository.save(stats);
    }

    // 辅助方法
    private String getMessageId(Message message) throws MessagingException {
        String[] messageIds = message.getHeader("Message-ID");
        return messageIds != null && messageIds.length > 0 ? messageIds[0] : null;
    }

    private String getSenderAddress(Message message) throws MessagingException {
        Address[] from = message.getFrom();
        return from != null && from.length > 0 ? from[0].toString() : null;
    }

    private String getRecipientAddress(Message message) throws MessagingException {
        Address[] to = message.getRecipients(Message.RecipientType.TO);
        return to != null && to.length > 0 ? to[0].toString() : null;
    }

    private LocalDateTime convertToLocalDateTime(Date date) {
        return date != null ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    }

    private String getContentSummary(Message message) {
        try {
            Object content = message.getContent();
            if (content instanceof String) {
                String text = (String) content;
                return text.length() > 500 ? text.substring(0, 500) + "..." : text;
            } else if (content instanceof MimeMessage) {
                // 处理复杂邮件内容
                return "复杂邮件内容";
            }
        } catch (Exception e) {
            log.warn("获取邮件内容摘要失败: {}", e.getMessage());
        }
        return "无法获取内容";
    }

    private boolean isAutoReply(Message message) {
        try {
            String subject = message.getSubject();
            if (subject != null) {
                for (Pattern pattern : AUTO_REPLY_PATTERNS) {
                    if (pattern.matcher(subject).matches()) {
                        return true;
                    }
                }
            }

            // 检查邮件头
            String[] autoReplyHeaders = message.getHeader("Auto-Submitted");
            if (autoReplyHeaders != null && autoReplyHeaders.length > 0) {
                return !"no".equals(autoReplyHeaders[0]);
            }

            String[] precedenceHeaders = message.getHeader("Precedence");
            if (precedenceHeaders != null && precedenceHeaders.length > 0) {
                return "auto_reply".equals(precedenceHeaders[0]) || "bulk".equals(precedenceHeaders[0]);
            }

        } catch (Exception e) {
            log.warn("判断自动回复失败: {}", e.getMessage());
        }
        return false;
    }

    private MailPlan findRelatedMailPlan(String subject) {
        if (subject == null) return null;
        
        List<MailPlan> mailPlans = mailPlanRepository.findAllActive();
        for (MailPlan mailPlan : mailPlans) {
            if (subject.contains(mailPlan.getSubject()) || mailPlan.getSubject().contains(subject)) {
                return mailPlan;
            }
        }
        return null;
    }
}
