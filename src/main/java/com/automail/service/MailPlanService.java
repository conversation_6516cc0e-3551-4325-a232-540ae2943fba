package com.automail.service;

import com.automail.entity.MailPlan;
import com.automail.entity.MailRecord;
import com.automail.entity.MailSyncStats;
import com.automail.repository.MailPlanRepository;
import com.automail.repository.MailRecordRepository;
import com.automail.repository.MailSyncStatsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 邮件计划服务
 */
@Slf4j
@Service
public class MailPlanService {

    @Autowired
    private MailPlanRepository mailPlanRepository;

    @Autowired
    private MailRecordRepository mailRecordRepository;

    @Autowired
    private MailSyncStatsRepository mailSyncStatsRepository;

    @Autowired
    private MailSyncService mailSyncService;

    /**
     * 获取所有邮件计划
     */
    public List<MailPlan> getAllMailPlans() {
        return mailPlanRepository.findAllNotDeleted();
    }

    /**
     * 分页获取邮件计划
     */
    public Page<MailPlan> getMailPlans(Pageable pageable) {
        return mailPlanRepository.findAll(pageable);
    }

    /**
     * 根据ID获取邮件计划
     */
    public Optional<MailPlan> getMailPlanById(Long id) {
        return mailPlanRepository.findById(id);
    }

    /**
     * 创建邮件计划
     */
    @Transactional
    public MailPlan createMailPlan(MailPlan mailPlan) {
        mailPlan.setCreatedAt(LocalDateTime.now());
        mailPlan.setUpdatedAt(LocalDateTime.now());
        return mailPlanRepository.save(mailPlan);
    }

    /**
     * 更新邮件计划
     */
    @Transactional
    public MailPlan updateMailPlan(MailPlan mailPlan) {
        mailPlan.setUpdatedAt(LocalDateTime.now());
        return mailPlanRepository.save(mailPlan);
    }

    /**
     * 删除邮件计划
     */
    @Transactional
    public void deleteMailPlan(Long id) {
        Optional<MailPlan> mailPlanOpt = mailPlanRepository.findById(id);
        if (mailPlanOpt.isPresent()) {
            MailPlan mailPlan = mailPlanOpt.get();
            mailPlan.setDeleted(true);
            mailPlan.setUpdatedAt(LocalDateTime.now());
            mailPlanRepository.save(mailPlan);
        }
    }

    /**
     * 启动邮件计划
     */
    @Transactional
    public void startMailPlan(Long id) {
        Optional<MailPlan> mailPlanOpt = mailPlanRepository.findById(id);
        if (mailPlanOpt.isPresent()) {
            MailPlan mailPlan = mailPlanOpt.get();
            mailPlan.setStatus(MailPlan.PlanStatus.ACTIVE);
            mailPlan.setEnabled(true);
            if (mailPlan.getStartTime() == null) {
                mailPlan.setStartTime(LocalDateTime.now());
            }
            mailPlan.setUpdatedAt(LocalDateTime.now());
            mailPlanRepository.save(mailPlan);
            
            log.info("邮件计划已启动: {}", mailPlan.getPlanName());
        }
    }

    /**
     * 暂停邮件计划
     */
    @Transactional
    public void pauseMailPlan(Long id) {
        Optional<MailPlan> mailPlanOpt = mailPlanRepository.findById(id);
        if (mailPlanOpt.isPresent()) {
            MailPlan mailPlan = mailPlanOpt.get();
            mailPlan.setStatus(MailPlan.PlanStatus.PAUSED);
            mailPlan.setUpdatedAt(LocalDateTime.now());
            mailPlanRepository.save(mailPlan);
            
            log.info("邮件计划已暂停: {}", mailPlan.getPlanName());
        }
    }

    /**
     * 停止邮件计划
     */
    @Transactional
    public void stopMailPlan(Long id) {
        Optional<MailPlan> mailPlanOpt = mailPlanRepository.findById(id);
        if (mailPlanOpt.isPresent()) {
            MailPlan mailPlan = mailPlanOpt.get();
            mailPlan.setStatus(MailPlan.PlanStatus.COMPLETED);
            mailPlan.setEnabled(false);
            mailPlan.setEndTime(LocalDateTime.now());
            mailPlan.setUpdatedAt(LocalDateTime.now());
            mailPlanRepository.save(mailPlan);
            
            log.info("邮件计划已停止: {}", mailPlan.getPlanName());
        }
    }

    /**
     * 获取活跃的邮件计划
     */
    public List<MailPlan> getActiveMailPlans() {
        return mailPlanRepository.findAllActive();
    }

    /**
     * 刷新邮件计划统计数据
     */
    @Transactional
    public void refreshMailPlanStats(Long id) {
        try {
            mailSyncService.updateMailPlanStats();
            log.info("邮件计划统计数据刷新完成: {}", id);
        } catch (Exception e) {
            log.error("刷新邮件计划统计数据失败: {}, 错误: {}", id, e.getMessage());
            throw new RuntimeException("刷新统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取邮件计划的发送记录
     */
    public Page<MailRecord> getMailPlanRecords(Long mailPlanId, Pageable pageable) {
        return mailRecordRepository.findByMailPlanId(mailPlanId, pageable);
    }

    /**
     * 获取邮件计划的统计历史
     */
    public List<MailSyncStats> getMailPlanStatsHistory(Long mailPlanId) {
        return mailSyncStatsRepository.findByMailPlanId(mailPlanId);
    }

    /**
     * 获取邮件计划的统计历史（指定日期范围）
     */
    public List<MailSyncStats> getMailPlanStatsHistory(Long mailPlanId, LocalDate startDate, LocalDate endDate) {
        return mailSyncStatsRepository.findByMailPlanIdAndDateRange(mailPlanId, startDate, endDate);
    }

    /**
     * 获取邮件计划的汇总统计
     */
    public MailPlanSummaryStats getMailPlanSummaryStats(Long mailPlanId) {
        Optional<MailPlan> mailPlanOpt = mailPlanRepository.findById(mailPlanId);
        if (mailPlanOpt.isEmpty()) {
            throw new RuntimeException("邮件计划不存在");
        }

        MailPlan mailPlan = mailPlanOpt.get();
        
        // 获取汇总数据
        Object[] summaryData = mailSyncStatsRepository.sumStatsByMailPlanId(mailPlanId);
        
        MailPlanSummaryStats summary = new MailPlanSummaryStats();
        summary.setMailPlanId(mailPlanId);
        summary.setMailPlanName(mailPlan.getPlanName());
        summary.setSentCount(mailPlan.getSentCount());
        summary.setPendingCount(mailPlan.getPendingCount());
        summary.setAutoReplyCount(mailPlan.getAutoReplyCount());
        summary.setManualReplyCount(mailPlan.getManualReplyCount());
        summary.setTotalReplyCount(mailPlan.getAutoReplyCount() + mailPlan.getManualReplyCount());
        
        // 计算回复率
        if (mailPlan.getSentCount() > 0) {
            double replyRate = (double) summary.getTotalReplyCount() / mailPlan.getSentCount() * 100;
            summary.setReplyRate(replyRate);
        } else {
            summary.setReplyRate(0.0);
        }
        
        summary.setLastSyncTime(mailPlan.getLastSyncTime());
        summary.setStatus(mailPlan.getStatus().name());
        
        return summary;
    }

    /**
     * 邮件计划汇总统计数据类
     */
    public static class MailPlanSummaryStats {
        private Long mailPlanId;
        private String mailPlanName;
        private Integer sentCount;
        private Integer pendingCount;
        private Integer autoReplyCount;
        private Integer manualReplyCount;
        private Integer totalReplyCount;
        private Double replyRate;
        private LocalDateTime lastSyncTime;
        private String status;

        // Getters and Setters
        public Long getMailPlanId() { return mailPlanId; }
        public void setMailPlanId(Long mailPlanId) { this.mailPlanId = mailPlanId; }

        public String getMailPlanName() { return mailPlanName; }
        public void setMailPlanName(String mailPlanName) { this.mailPlanName = mailPlanName; }

        public Integer getSentCount() { return sentCount; }
        public void setSentCount(Integer sentCount) { this.sentCount = sentCount; }

        public Integer getPendingCount() { return pendingCount; }
        public void setPendingCount(Integer pendingCount) { this.pendingCount = pendingCount; }

        public Integer getAutoReplyCount() { return autoReplyCount; }
        public void setAutoReplyCount(Integer autoReplyCount) { this.autoReplyCount = autoReplyCount; }

        public Integer getManualReplyCount() { return manualReplyCount; }
        public void setManualReplyCount(Integer manualReplyCount) { this.manualReplyCount = manualReplyCount; }

        public Integer getTotalReplyCount() { return totalReplyCount; }
        public void setTotalReplyCount(Integer totalReplyCount) { this.totalReplyCount = totalReplyCount; }

        public Double getReplyRate() { return replyRate; }
        public void setReplyRate(Double replyRate) { this.replyRate = replyRate; }

        public LocalDateTime getLastSyncTime() { return lastSyncTime; }
        public void setLastSyncTime(LocalDateTime lastSyncTime) { this.lastSyncTime = lastSyncTime; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
