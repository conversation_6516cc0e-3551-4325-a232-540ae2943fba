package com.automail.dto;

import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页结果类
 */
@Data
public class PageResult<T> {
    
    private List<T> records;
    private Long total;
    private Integer size;
    private Integer current;
    private Integer pages;

    public PageResult() {}

    public PageResult(Page<T> page) {
        this.records = page.getContent();
        this.total = page.getTotalElements();
        this.size = page.getSize();
        this.current = page.getNumber() + 1; // Spring Data JPA页码从0开始，前端通常从1开始
        this.pages = page.getTotalPages();
    }

    public PageResult(List<T> records, Long total, Integer size, Integer current) {
        this.records = records;
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = (int) Math.ceil((double) total / size);
    }
}
