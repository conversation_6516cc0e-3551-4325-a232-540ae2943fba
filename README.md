# 自动发送邮件系统

一个基于Spring Boot + Vue3 + Element Plus的自动邮件发送系统，支持多种邮件协议，可配置发送计划，自动按顺序发送邮件。

## 功能特性

- 🔐 用户认证与权限管理
- 📧 多邮箱配置管理（支持SMTP/POP3/IMAP/Exchange）
- 👥 收件人列表管理
- 📅 邮件发送计划配置
- ⏰ 定时自动发送（可配置3-5分钟间隔）
- 📎 附件上传与管理
- 📊 发送记录与统计
- 🎯 按顺序逐一发送邮件
- 📈 实时监控与状态展示
- 🔄 自动同步邮件数据（已投数量、未投数量、自动回复数量、手工回复数量）
- 📋 邮件计划统计面板
- ⚡ 定时自动同步与统计更新

## 技术栈

### 后端
- **框架**: Spring Boot 3.2.0
- **数据库**: MySQL 8.0
- **安全**: Spring Security + JWT
- **ORM**: Spring Data JPA
- **邮件**: Spring Boot Starter Mail
- **构建工具**: Maven

### 前端
- **框架**: Vue 3
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 项目结构

```
auto-send-mail/
├── src/main/java/com/automail/          # 后端源码
│   ├── config/                          # 配置类
│   ├── controller/                      # 控制器
│   ├── entity/                          # 实体类
│   ├── repository/                      # 数据访问层
│   ├── service/                         # 业务逻辑层
│   ├── dto/                            # 数据传输对象
│   ├── utils/                          # 工具类
│   └── scheduler/                      # 定时任务
├── src/main/resources/                  # 资源文件
│   ├── application.yml                  # 应用配置
│   └── db/init.sql                     # 数据库初始化脚本
├── frontend/                           # 前端项目
│   ├── src/
│   │   ├── components/                 # 组件
│   │   ├── views/                      # 页面
│   │   ├── api/                        # API接口
│   │   ├── store/                      # 状态管理
│   │   ├── router/                     # 路由配置
│   │   └── utils/                      # 工具函数
│   ├── package.json                    # 前端依赖
│   └── vite.config.js                  # Vite配置
├── uploads/                            # 文件上传目录
├── logs/                               # 日志目录
├── pom.xml                             # Maven配置
└── README.md                           # 项目说明
```

## 快速开始

### 环境要求

- JDK 17+
- MySQL 8.0+
- Node.js 16+
- Maven 3.6+

### 后端启动

1. **创建数据库**
   ```sql
   CREATE DATABASE auto_send_mail DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **修改数据库配置**
   编辑 `src/main/resources/application.yml`，修改数据库连接信息：
   ```yaml
   spring:
     datasource:
       url: ***************************************************************************************************************************
       username: your_username
       password: your_password
   ```

3. **运行初始化脚本**
   ```bash
   mysql -u your_username -p auto_send_mail < src/main/resources/db/init.sql
   ```

4. **启动后端服务**
   ```bash
   mvn spring-boot:run
   ```

   后端服务将在 http://localhost:8080 启动

### 前端启动

1. **安装依赖**
   ```bash
   cd frontend
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

   前端服务将在 http://localhost:3000 启动

### 默认账号

- 用户名: `admin`
- 密码: `admin123`

## 主要功能模块

### 1. 邮箱配置管理
- 支持多种邮件协议（SMTP、POP3、IMAP、Exchange）
- 配置邮件服务器信息和认证
- 支持SSL/TLS加密

### 2. 收件人管理
- 批量导入收件人
- 分组管理
- 排序配置

### 3. 发送计划
- 创建邮件发送计划
- 配置发送间隔（3-5分钟可调）
- 设置计划开始和结束时间
- 支持HTML和纯文本邮件

### 4. 附件管理
- 文件上传
- 多种文件格式支持
- 文件大小限制配置

### 5. 发送记录
- 实时发送状态监控
- 发送历史记录
- 失败重试机制

### 6. 邮件同步统计
- **已投数量**: 自动统计邮件平台已发送的邮件数量
- **未投数量**: 统计待发送的邮件数量
- **自动回复数量**: 识别并统计邮件平台的自动回复邮件
- **手工回复数量**: 统计非自动回复的人工回复邮件
- **回复率计算**: 自动计算回复率（总回复数/已发送数）
- **定时同步**: 每10分钟自动同步邮件数据，每小时更新统计
- **手动刷新**: 支持手动触发数据同步和统计更新

## 配置说明

### 邮件发送配置

在 `application.yml` 中可以配置：

```yaml
app:
  mail:
    default-interval: 300  # 默认发送间隔（秒）
    max-retry: 3          # 最大重试次数
```

### 文件上传配置

```yaml
app:
  upload:
    path: ./uploads/      # 上传文件存储路径
    max-size: 52428800   # 最大文件大小（50MB）
```

## API文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

### 邮箱配置接口
- `GET /api/email-config` - 获取邮箱配置列表
- `POST /api/email-config` - 创建邮箱配置
- `PUT /api/email-config/{id}` - 更新邮箱配置
- `DELETE /api/email-config/{id}` - 删除邮箱配置

### 收件人接口
- `GET /api/recipients` - 获取收件人列表
- `POST /api/recipients` - 创建收件人
- `PUT /api/recipients/{id}` - 更新收件人
- `DELETE /api/recipients/{id}` - 删除收件人

## 部署说明

### 生产环境部署

1. **打包后端**
   ```bash
   mvn clean package -DskipTests
   ```

2. **打包前端**
   ```bash
   cd frontend
   npm run build
   ```

3. **部署**
   - 将后端jar包部署到服务器
   - 将前端dist目录部署到Web服务器（如Nginx）
   - 配置反向代理

### Docker部署

TODO: 添加Docker配置文件

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请提交 Issue 或联系开发者。
