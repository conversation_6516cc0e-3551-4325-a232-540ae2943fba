import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/store/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/Layout.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘' }
      },
      {
        path: '/email-config',
        name: 'EmailConfig',
        component: () => import('@/views/EmailConfig.vue'),
        meta: { title: '邮箱配置' }
      },
      {
        path: '/recipients',
        name: 'Recipients',
        component: () => import('@/views/Recipients.vue'),
        meta: { title: '收件人管理' }
      },
      {
        path: '/mail-plans',
        name: 'MailPlans',
        component: () => import('@/views/MailPlans.vue'),
        meta: { title: '发送计划' }
      },
      {
        path: '/mail-records',
        name: 'MailRecords',
        component: () => import('@/views/MailRecords.vue'),
        meta: { title: '发送记录' }
      },
      {
        path: '/attachments',
        name: 'Attachments',
        component: () => import('@/views/Attachments.vue'),
        meta: { title: '附件管理' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth !== false && !userStore.token) {
    next('/login')
  } else if (to.path === '/login' && userStore.token) {
    next('/')
  } else {
    next()
  }
})

export default router
