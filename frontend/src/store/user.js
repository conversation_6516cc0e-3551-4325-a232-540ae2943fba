import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as apiLogin, getCurrentUser } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)

  // 登录
  const login = async (loginData) => {
    try {
      const response = await apiLogin(loginData)
      if (response.code === 200) {
        token.value = response.data.token
        userInfo.value = {
          username: response.data.username,
          email: response.data.email,
          realName: response.data.realName,
          role: response.data.role
        }
        localStorage.setItem('token', token.value)
        return { success: true }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: '登录失败，请检查网络连接' }
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await getCurrentUser()
      if (response.code === 200) {
        userInfo.value = response.data
        return true
      } else {
        logout()
        return false
      }
    } catch (error) {
      logout()
      return false
    }
  }

  return {
    token,
    userInfo,
    login,
    logout,
    fetchUserInfo
  }
})
