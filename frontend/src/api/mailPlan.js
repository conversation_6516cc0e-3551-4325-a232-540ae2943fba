import request from '@/utils/request'

/**
 * 获取邮件计划列表（分页）
 */
export function getMailPlans(params) {
  return request({
    url: '/mail-plans',
    method: 'get',
    params
  })
}

/**
 * 获取所有邮件计划（不分页）
 */
export function getAllMailPlans() {
  return request({
    url: '/mail-plans/all',
    method: 'get'
  })
}

/**
 * 根据ID获取邮件计划
 */
export function getMailPlan(id) {
  return request({
    url: `/mail-plans/${id}`,
    method: 'get'
  })
}

/**
 * 创建邮件计划
 */
export function createMailPlan(data) {
  return request({
    url: '/mail-plans',
    method: 'post',
    data
  })
}

/**
 * 更新邮件计划
 */
export function updateMailPlan(id, data) {
  return request({
    url: `/mail-plans/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除邮件计划
 */
export function deleteMailPlan(id) {
  return request({
    url: `/mail-plans/${id}`,
    method: 'delete'
  })
}

/**
 * 启动邮件计划
 */
export function startMailPlan(id) {
  return request({
    url: `/mail-plans/${id}/start`,
    method: 'post'
  })
}

/**
 * 暂停邮件计划
 */
export function pauseMailPlan(id) {
  return request({
    url: `/mail-plans/${id}/pause`,
    method: 'post'
  })
}

/**
 * 停止邮件计划
 */
export function stopMailPlan(id) {
  return request({
    url: `/mail-plans/${id}/stop`,
    method: 'post'
  })
}

/**
 * 刷新邮件计划统计数据
 */
export function refreshMailPlanStats(id) {
  return request({
    url: `/mail-plans/${id}/refresh-stats`,
    method: 'post'
  })
}

/**
 * 同步邮件计划数据
 */
export function syncMailPlan(id) {
  return request({
    url: `/mail-plans/${id}/sync`,
    method: 'post'
  })
}

/**
 * 获取邮件计划的发送记录
 */
export function getMailPlanRecords(id, params) {
  return request({
    url: `/mail-plans/${id}/records`,
    method: 'get',
    params
  })
}

/**
 * 获取邮件计划的统计历史
 */
export function getMailPlanStats(id) {
  return request({
    url: `/mail-plans/${id}/stats`,
    method: 'get'
  })
}

/**
 * 获取邮件计划的统计历史（指定日期范围）
 */
export function getMailPlanStatsRange(id, startDate, endDate) {
  return request({
    url: `/mail-plans/${id}/stats/range`,
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

/**
 * 获取邮件计划的汇总统计
 */
export function getMailPlanSummary(id) {
  return request({
    url: `/mail-plans/${id}/summary`,
    method: 'get'
  })
}

/**
 * 获取活跃的邮件计划
 */
export function getActiveMailPlans() {
  return request({
    url: '/mail-plans/active',
    method: 'get'
  })
}
