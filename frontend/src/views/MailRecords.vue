<template>
  <div class="mail-records">
    <div class="page-header">
      <h2>发送记录</h2>
      <el-button @click="refreshRecords">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索邮件主题或收件人"
            clearable
            @keyup.enter="loadRecords"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="发送状态" clearable>
            <el-option label="待发送" value="PENDING" />
            <el-option label="发送中" value="SENDING" />
            <el-option label="发送成功" value="SUCCESS" />
            <el-option label="发送失败" value="FAILED" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.successCount || 0 }}</div>
              <div class="stats-label">发送成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingCount || 0 }}</div>
              <div class="stats-label">待发送</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon info">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.sendingCount || 0 }}</div>
              <div class="stats-label">发送中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.failedCount || 0 }}</div>
              <div class="stats-label">发送失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 发送记录列表 -->
    <el-card>
      <el-table
        :data="records"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="subject" label="邮件主题" min-width="200" show-overflow-tooltip />
        <el-table-column label="收件人" min-width="180">
          <template #default="scope">
            <div>{{ scope.row.recipient?.name }}</div>
            <div class="text-muted">{{ scope.row.recipient?.emailAddress }}</div>
          </template>
        </el-table-column>
        <el-table-column label="发件邮箱" min-width="150">
          <template #default="scope">
            {{ scope.row.emailConfig?.emailAddress }}
          </template>
        </el-table-column>
        <el-table-column prop="sendStatus" label="发送状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.sendStatus)" size="small">
              {{ getStatusText(scope.row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="150">
          <template #default="scope">
            <span v-if="scope.row.sendTime">
              {{ formatDateTime(scope.row.sendTime) }}
            </span>
            <span v-else class="text-muted">未发送</span>
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试次数" width="100" />
        <el-table-column label="错误信息" min-width="200">
          <template #default="scope">
            <span v-if="scope.row.errorMessage" class="error-message">
              {{ scope.row.errorMessage }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.sendStatus === 'FAILED'"
              type="primary"
              size="small"
              @click="retryRecord(scope.row)"
            >
              重试
            </el-button>
            <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRecords"
          @current-change="loadRecords"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="发送记录详情"
      width="800px"
    >
      <div v-if="selectedRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="邮件主题">
            {{ selectedRecord.subject }}
          </el-descriptions-item>
          <el-descriptions-item label="发送状态">
            <el-tag :type="getStatusType(selectedRecord.sendStatus)" size="small">
              {{ getStatusText(selectedRecord.sendStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="收件人">
            {{ selectedRecord.recipient?.name }} ({{ selectedRecord.recipient?.emailAddress }})
          </el-descriptions-item>
          <el-descriptions-item label="发件邮箱">
            {{ selectedRecord.emailConfig?.emailAddress }}
          </el-descriptions-item>
          <el-descriptions-item label="发送时间">
            {{ selectedRecord.sendTime ? formatDateTime(selectedRecord.sendTime) : '未发送' }}
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            {{ selectedRecord.retryCount }}
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" :span="2">
            {{ selectedRecord.errorMessage || '无' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-section">
          <h4>邮件内容</h4>
          <div class="content-preview" v-html="selectedRecord.content"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const records = ref([])
const selectedRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  dateRange: []
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  successCount: 0,
  pendingCount: 0,
  sendingCount: 0,
  failedCount: 0
})

// 生命周期
onMounted(() => {
  loadRecords()
  loadStats()
})

// 方法
const loadRecords = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    records.value = [
      {
        id: 1,
        subject: '产品推广邮件',
        content: '<p>我们很高兴地宣布新产品的发布...</p>',
        recipient: {
          name: '张三',
          emailAddress: '<EMAIL>'
        },
        emailConfig: {
          emailAddress: '<EMAIL>'
        },
        sendStatus: 'SUCCESS',
        sendTime: '2024-01-15 10:30:00',
        retryCount: 0,
        errorMessage: null
      },
      {
        id: 2,
        subject: '活动通知',
        content: '<p>亲爱的客户，我们将举办一场重要活动...</p>',
        recipient: {
          name: '李四',
          emailAddress: '<EMAIL>'
        },
        emailConfig: {
          emailAddress: '<EMAIL>'
        },
        sendStatus: 'FAILED',
        sendTime: null,
        retryCount: 2,
        errorMessage: '邮件服务器连接超时'
      },
      {
        id: 3,
        subject: '系统更新通知',
        content: '<p>系统将在今晚进行维护更新...</p>',
        recipient: {
          name: '王五',
          emailAddress: '<EMAIL>'
        },
        emailConfig: {
          emailAddress: '<EMAIL>'
        },
        sendStatus: 'PENDING',
        sendTime: null,
        retryCount: 0,
        errorMessage: null
      }
    ]
    
    pagination.total = 3
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟统计数据
    Object.assign(stats, {
      successCount: 156,
      pendingCount: 23,
      sendingCount: 5,
      failedCount: 8
    })
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

const refreshRecords = () => {
  loadRecords()
  loadStats()
  ElMessage.success('数据已刷新')
}

const retryRecord = async (record) => {
  try {
    ElMessage.info('正在重试发送...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('重试发送成功')
    loadRecords()
  } catch (error) {
    ElMessage.error('重试发送失败')
  }
}

const viewDetail = (record) => {
  selectedRecord.value = record
  detailDialogVisible.value = true
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.dateRange = []
  loadRecords()
}

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'SENDING': 'info',
    'SUCCESS': 'success',
    'FAILED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待发送',
    'SENDING': '发送中',
    'SUCCESS': '发送成功',
    'FAILED': '发送失败'
  }
  return statusMap[status] || '未知'
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}
</script>

<style scoped>
.mail-records {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stats-icon.success {
  background-color: #67c23a;
}

.stats-icon.warning {
  background-color: #e6a23c;
}

.stats-icon.info {
  background-color: #409eff;
}

.stats-icon.danger {
  background-color: #f56c6c;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}

.record-detail {
  padding: 0;
}

.content-section {
  margin-top: 20px;
}

.content-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.content-preview {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  max-height: 200px;
  overflow-y: auto;
}
</style>
