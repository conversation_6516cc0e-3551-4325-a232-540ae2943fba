<template>
  <div class="recipients">
    <div class="page-header">
      <h2>收件人管理</h2>
      <div>
        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建收件人
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索姓名或邮箱地址"
            clearable
            @keyup.enter="loadRecipients"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.groupName" placeholder="分组" clearable>
            <el-option label="客户" value="客户" />
            <el-option label="合作伙伴" value="合作伙伴" />
            <el-option label="媒体" value="媒体" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadRecipients">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 收件人列表 -->
    <el-card>
      <el-table
        :data="recipients"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="姓名" min-width="120" />
        <el-table-column prop="emailAddress" label="邮箱地址" min-width="200" />
        <el-table-column prop="groupName" label="分组" width="100" />
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'" size="small">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="editRecipient(scope.row)">编辑</el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteRecipient(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRecipients"
          @current-change="loadRecipients"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="emailAddress">
              <el-input v-model="form.emailAddress" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分组">
              <el-select v-model="form.groupName" placeholder="选择分组" style="width: 100%" allow-create filterable>
                <el-option label="客户" value="客户" />
                <el-option label="合作伙伴" value="合作伙伴" />
                <el-option label="媒体" value="媒体" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序序号">
              <el-input-number v-model="form.sortOrder" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="是否启用">
          <el-switch v-model="form.enabled" />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入收件人"
      width="600px"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <p>请上传CSV或Excel文件，文件格式要求：</p>
          <p>第一列：姓名，第二列：邮箱地址，第三列：分组（可选），第四列：备注（可选）</p>
        </el-alert>
        
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".csv,.xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 CSV/Excel 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="importRecipients" :loading="importing">
          导入
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const importing = ref(false)
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const dialogTitle = ref('新建收件人')
const recipients = ref([])
const selectedRecipients = ref([])
const importFile = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  groupName: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  emailAddress: '',
  groupName: '',
  sortOrder: 0,
  enabled: true,
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  emailAddress: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const formRef = ref()

// 生命周期
onMounted(() => {
  loadRecipients()
})

// 方法
const loadRecipients = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    recipients.value = [
      {
        id: 1,
        name: '张三',
        emailAddress: '<EMAIL>',
        groupName: '客户',
        sortOrder: 1,
        enabled: true,
        remark: '重要客户'
      },
      {
        id: 2,
        name: '李四',
        emailAddress: '<EMAIL>',
        groupName: '合作伙伴',
        sortOrder: 2,
        enabled: true,
        remark: '长期合作伙伴'
      },
      {
        id: 3,
        name: '王五',
        emailAddress: '<EMAIL>',
        groupName: '媒体',
        sortOrder: 3,
        enabled: false,
        remark: '媒体联系人'
      }
    ]
    
    pagination.total = 3
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  dialogTitle.value = '新建收件人'
  resetForm()
  dialogVisible.value = true
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const editRecipient = (recipient) => {
  dialogTitle.value = '编辑收件人'
  Object.assign(form, recipient)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    emailAddress: '',
    groupName: '',
    sortOrder: 0,
    enabled: true,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        ElMessage.success(form.id ? '更新成功' : '创建成功')
        dialogVisible.value = false
        loadRecipients()
      } catch (error) {
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const deleteRecipient = async (recipient) => {
  try {
    await ElMessageBox.confirm(`确定要删除收件人"${recipient.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    loadRecipients()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedRecipients.value = selection
}

const handleFileChange = (file) => {
  importFile.value = file
}

const importRecipients = async () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importing.value = true
  try {
    // 模拟文件导入
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('导入成功')
    importDialogVisible.value = false
    loadRecipients()
  } catch (error) {
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.groupName = ''
  loadRecipients()
}
</script>

<style scoped>
.recipients {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.import-content {
  padding: 0;
}

.upload-demo {
  width: 100%;
}
</style>
