<template>
  <div class="mail-plans">
    <div class="page-header">
      <h2>发送计划管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新建计划
      </el-button>
    </div>

    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索计划名称"
            clearable
            @keyup.enter="loadMailPlans"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="暂停" value="PAUSED" />
            <el-option label="完成" value="COMPLETED" />
            <el-option label="取消" value="CANCELLED" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadMailPlans">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="syncAllPlans">
            <el-icon><Refresh /></el-icon>
            同步数据
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 邮件计划列表 -->
    <el-card>
      <el-table
        :data="mailPlans"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="planName" label="计划名称" min-width="150">
          <template #default="scope">
            <el-link type="primary" @click="viewPlanDetail(scope.row)">
              {{ scope.row.planName }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="subject" label="邮件主题" min-width="200" show-overflow-tooltip />
        
        <el-table-column label="发送统计" min-width="300">
          <template #default="scope">
            <div class="stats-container">
              <div class="stats-item">
                <span class="stats-label">已投:</span>
                <el-tag type="success" size="small">{{ scope.row.sentCount || 0 }}</el-tag>
              </div>
              <div class="stats-item">
                <span class="stats-label">未投:</span>
                <el-tag type="warning" size="small">{{ scope.row.pendingCount || 0 }}</el-tag>
              </div>
              <div class="stats-item">
                <span class="stats-label">自动回复:</span>
                <el-tag type="info" size="small">{{ scope.row.autoReplyCount || 0 }}</el-tag>
              </div>
              <div class="stats-item">
                <span class="stats-label">手工回复:</span>
                <el-tag type="primary" size="small">{{ scope.row.manualReplyCount || 0 }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="回复率" width="100">
          <template #default="scope">
            <span v-if="scope.row.sentCount > 0">
              {{ calculateReplyRate(scope.row) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sendInterval" label="发送间隔" width="100">
          <template #default="scope">
            {{ scope.row.sendInterval }}秒
          </template>
        </el-table-column>

        <el-table-column label="最后同步" width="150">
          <template #default="scope">
            <span v-if="scope.row.lastSyncTime">
              {{ formatDateTime(scope.row.lastSyncTime) }}
            </span>
            <span v-else class="text-muted">未同步</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button-group>
              <el-button
                v-if="scope.row.status === 'DRAFT' || scope.row.status === 'PAUSED'"
                type="success"
                size="small"
                @click="startPlan(scope.row)"
              >
                启动
              </el-button>
              <el-button
                v-if="scope.row.status === 'ACTIVE'"
                type="warning"
                size="small"
                @click="pausePlan(scope.row)"
              >
                暂停
              </el-button>
              <el-button
                v-if="scope.row.status === 'ACTIVE' || scope.row.status === 'PAUSED'"
                type="danger"
                size="small"
                @click="stopPlan(scope.row)"
              >
                停止
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="refreshStats(scope.row)"
              >
                刷新
              </el-button>
            </el-button-group>
            <br style="margin: 5px 0;">
            <el-button-group>
              <el-button size="small" @click="editPlan(scope.row)">编辑</el-button>
              <el-button size="small" @click="viewRecords(scope.row)">记录</el-button>
              <el-button
                type="danger"
                size="small"
                @click="deletePlan(scope.row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadMailPlans"
          @current-change="loadMailPlans"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划名称" prop="planName">
              <el-input v-model="form.planName" placeholder="请输入计划名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送间隔" prop="sendInterval">
              <el-input-number
                v-model="form.sendInterval"
                :min="180"
                :max="600"
                placeholder="秒"
                style="width: 100%"
              />
              <div class="form-tip">建议3-5分钟（180-300秒）</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="邮件主题" prop="subject">
          <el-input v-model="form.subject" placeholder="请输入邮件主题" />
        </el-form-item>
        
        <el-form-item label="邮件内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="8"
            placeholder="请输入邮件内容"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="内容类型" prop="contentType">
              <el-select v-model="form.contentType" style="width: 100%">
                <el-option label="HTML" value="HTML" />
                <el-option label="纯文本" value="TEXT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新建计划')
const mailPlans = ref([])
const selectedPlans = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  planName: '',
  subject: '',
  content: '',
  contentType: 'HTML',
  sendInterval: 300,
  startTime: null,
  endTime: null,
  remark: ''
})

// 表单验证规则
const formRules = {
  planName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' }
  ],
  sendInterval: [
    { required: true, message: '请输入发送间隔', trigger: 'blur' },
    { type: 'number', min: 180, max: 600, message: '发送间隔应在180-600秒之间', trigger: 'blur' }
  ]
}

const formRef = ref()

// 生命周期
onMounted(() => {
  loadMailPlans()
})

// 方法
const loadMailPlans = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    mailPlans.value = [
      {
        id: 1,
        planName: '产品推广计划',
        subject: '新产品发布通知',
        content: '我们很高兴地宣布新产品的发布...',
        contentType: 'HTML',
        sendInterval: 300,
        status: 'ACTIVE',
        sentCount: 156,
        pendingCount: 23,
        autoReplyCount: 12,
        manualReplyCount: 8,
        lastSyncTime: '2024-01-15 10:30:00',
        startTime: '2024-01-10 09:00:00',
        endTime: null,
        remark: '重要推广活动'
      },
      {
        id: 2,
        planName: '客户回访计划',
        subject: '客户满意度调研',
        content: '亲爱的客户，我们希望了解您的使用体验...',
        contentType: 'HTML',
        sendInterval: 240,
        status: 'PAUSED',
        sentCount: 89,
        pendingCount: 45,
        autoReplyCount: 5,
        manualReplyCount: 15,
        lastSyncTime: '2024-01-15 09:15:00',
        startTime: '2024-01-08 14:00:00',
        endTime: null,
        remark: '定期客户回访'
      }
    ]
    
    pagination.total = 2
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  dialogTitle.value = '新建计划'
  resetForm()
  dialogVisible.value = true
}

const editPlan = (plan) => {
  dialogTitle.value = '编辑计划'
  Object.assign(form, plan)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    planName: '',
    subject: '',
    content: '',
    contentType: 'HTML',
    sendInterval: 300,
    startTime: null,
    endTime: null,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        ElMessage.success(form.id ? '更新成功' : '创建成功')
        dialogVisible.value = false
        loadMailPlans()
      } catch (error) {
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const startPlan = async (plan) => {
  try {
    await ElMessageBox.confirm(`确定要启动计划"${plan.planName}"吗？`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('启动成功')
    loadMailPlans()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('启动失败')
    }
  }
}

const pausePlan = async (plan) => {
  try {
    await ElMessageBox.confirm(`确定要暂停计划"${plan.planName}"吗？`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('暂停成功')
    loadMailPlans()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('暂停失败')
    }
  }
}

const stopPlan = async (plan) => {
  try {
    await ElMessageBox.confirm(`确定要停止计划"${plan.planName}"吗？停止后无法恢复。`, '确认操作', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('停止成功')
    loadMailPlans()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止失败')
    }
  }
}

const refreshStats = async (plan) => {
  try {
    ElMessage.info('正在刷新统计数据...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('统计数据刷新成功')
    loadMailPlans()
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const syncAllPlans = async () => {
  try {
    ElMessage.info('正在同步所有计划数据...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('数据同步成功')
    loadMailPlans()
  } catch (error) {
    ElMessage.error('同步失败')
  }
}

const deletePlan = async (plan) => {
  try {
    await ElMessageBox.confirm(`确定要删除计划"${plan.planName}"吗？此操作不可恢复。`, '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    loadMailPlans()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const viewPlanDetail = (plan) => {
  // 跳转到计划详情页面
  ElMessage.info('跳转到计划详情页面')
}

const viewRecords = (plan) => {
  // 跳转到发送记录页面
  ElMessage.info('跳转到发送记录页面')
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  loadMailPlans()
}

const handleSelectionChange = (selection) => {
  selectedPlans.value = selection
}

const calculateReplyRate = (plan) => {
  if (plan.sentCount === 0) return 0
  const totalReplies = (plan.autoReplyCount || 0) + (plan.manualReplyCount || 0)
  return ((totalReplies / plan.sentCount) * 100).toFixed(1)
}

const getStatusType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'ACTIVE': 'success',
    'PAUSED': 'warning',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'ACTIVE': '活跃',
    'PAUSED': '暂停',
    'COMPLETED': '完成',
    'CANCELLED': '取消'
  }
  return statusMap[status] || '未知'
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('MM-DD HH:mm')
}
</script>

<style scoped>
.mail-plans {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.text-muted {
  color: #999;
}
</style>
