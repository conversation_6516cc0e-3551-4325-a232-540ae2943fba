<template>
  <div class="attachments">
    <div class="page-header">
      <h2>附件管理</h2>
      <el-button type="primary" @click="showUploadDialog">
        <el-icon><Upload /></el-icon>
        上传附件
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索文件名"
            clearable
            @keyup.enter="loadAttachments"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.mailPlanId" placeholder="关联计划" clearable>
            <el-option
              v-for="plan in mailPlans"
              :key="plan.id"
              :label="plan.planName"
              :value="plan.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadAttachments">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 附件列表 -->
    <el-card>
      <el-table
        :data="attachments"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="文件" min-width="200">
          <template #default="scope">
            <div class="file-info">
              <el-icon class="file-icon">
                <Document v-if="isDocument(scope.row.contentType)" />
                <Picture v-else-if="isImage(scope.row.contentType)" />
                <VideoPlay v-else-if="isVideo(scope.row.contentType)" />
                <Folder v-else />
              </el-icon>
              <div class="file-details">
                <div class="file-name">{{ scope.row.originalFilename }}</div>
                <div class="file-meta">{{ formatFileSize(scope.row.fileSize) }} • {{ scope.row.contentType }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="关联计划" min-width="150">
          <template #default="scope">
            <span v-if="scope.row.mailPlan">{{ scope.row.mailPlan.planName }}</span>
            <span v-else class="text-muted">未关联</span>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'" size="small">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="上传时间" width="150">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="downloadFile(scope.row)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button size="small" @click="editAttachment(scope.row)">编辑</el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteAttachment(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadAttachments"
          @current-change="loadAttachments"
        />
      </div>
    </el-card>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传附件"
      width="600px"
    >
      <el-form
        ref="uploadFormRef"
        :model="uploadForm"
        label-width="100px"
      >
        <el-form-item label="关联计划">
          <el-select v-model="uploadForm.mailPlanId" placeholder="选择邮件计划" style="width: 100%">
            <el-option
              v-for="plan in mailPlans"
              :key="plan.id"
              :label="plan.planName"
              :value="plan.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="文件上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
            multiple
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多文件上传，单个文件不超过 50MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="uploadForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="uploadFiles" :loading="uploading">
          上传
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑附件"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        label-width="100px"
      >
        <el-form-item label="文件名">
          <el-input v-model="editForm.originalFilename" readonly />
        </el-form-item>
        
        <el-form-item label="关联计划">
          <el-select v-model="editForm.mailPlanId" placeholder="选择邮件计划" style="width: 100%">
            <el-option label="不关联" :value="null" />
            <el-option
              v-for="plan in mailPlans"
              :key="plan.id"
              :label="plan.planName"
              :value="plan.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否启用">
          <el-switch v-model="editForm.enabled" />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateAttachment" :loading="updating">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const updating = ref(false)
const uploadDialogVisible = ref(false)
const editDialogVisible = ref(false)
const attachments = ref([])
const mailPlans = ref([])
const selectedAttachments = ref([])
const fileList = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  mailPlanId: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 上传表单
const uploadForm = reactive({
  mailPlanId: null,
  remark: ''
})

// 编辑表单
const editForm = reactive({
  id: null,
  originalFilename: '',
  mailPlanId: null,
  enabled: true,
  remark: ''
})

const uploadFormRef = ref()
const editFormRef = ref()

// 生命周期
onMounted(() => {
  loadAttachments()
  loadMailPlans()
})

// 方法
const loadAttachments = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    attachments.value = [
      {
        id: 1,
        originalFilename: '产品介绍.pdf',
        storedFilename: 'product_intro_20240115.pdf',
        fileSize: 2048576, // 2MB
        contentType: 'application/pdf',
        mailPlan: {
          id: 1,
          planName: '产品推广计划'
        },
        enabled: true,
        createdAt: '2024-01-15 10:30:00',
        remark: '产品详细介绍文档'
      },
      {
        id: 2,
        originalFilename: '公司宣传图.jpg',
        storedFilename: 'company_banner_20240115.jpg',
        fileSize: 1536000, // 1.5MB
        contentType: 'image/jpeg',
        mailPlan: {
          id: 2,
          planName: '客户回访计划'
        },
        enabled: true,
        createdAt: '2024-01-15 09:15:00',
        remark: '公司宣传图片'
      },
      {
        id: 3,
        originalFilename: '演示视频.mp4',
        storedFilename: 'demo_video_20240115.mp4',
        fileSize: 10485760, // 10MB
        contentType: 'video/mp4',
        mailPlan: null,
        enabled: false,
        createdAt: '2024-01-14 16:20:00',
        remark: '产品演示视频'
      }
    ]
    
    pagination.total = 3
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadMailPlans = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    // 模拟数据
    mailPlans.value = [
      { id: 1, planName: '产品推广计划' },
      { id: 2, planName: '客户回访计划' },
      { id: 3, planName: '新用户欢迎' }
    ]
  } catch (error) {
    console.error('加载邮件计划失败', error)
  }
}

const showUploadDialog = () => {
  uploadForm.mailPlanId = null
  uploadForm.remark = ''
  fileList.value = []
  uploadDialogVisible.value = true
}

const handleFileChange = (file, files) => {
  fileList.value = files
}

const uploadFiles = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  
  uploading.value = true
  try {
    // 模拟文件上传
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('文件上传成功')
    uploadDialogVisible.value = false
    loadAttachments()
  } catch (error) {
    ElMessage.error('文件上传失败')
  } finally {
    uploading.value = false
  }
}

const editAttachment = (attachment) => {
  Object.assign(editForm, {
    id: attachment.id,
    originalFilename: attachment.originalFilename,
    mailPlanId: attachment.mailPlan?.id || null,
    enabled: attachment.enabled,
    remark: attachment.remark
  })
  editDialogVisible.value = true
}

const updateAttachment = async () => {
  updating.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('更新成功')
    editDialogVisible.value = false
    loadAttachments()
  } catch (error) {
    ElMessage.error('更新失败')
  } finally {
    updating.value = false
  }
}

const downloadFile = (attachment) => {
  // 模拟文件下载
  ElMessage.info(`正在下载文件: ${attachment.originalFilename}`)
  
  // 实际实现中，这里应该调用下载API或创建下载链接
  const link = document.createElement('a')
  link.href = '#' // 实际的文件下载URL
  link.download = attachment.originalFilename
  link.click()
}

const deleteAttachment = async (attachment) => {
  try {
    await ElMessageBox.confirm(`确定要删除文件"${attachment.originalFilename}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    loadAttachments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedAttachments.value = selection
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.mailPlanId = null
  loadAttachments()
}

// 工具方法
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateTime) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const isDocument = (contentType) => {
  return contentType && (
    contentType.includes('pdf') ||
    contentType.includes('doc') ||
    contentType.includes('txt') ||
    contentType.includes('excel') ||
    contentType.includes('sheet')
  )
}

const isImage = (contentType) => {
  return contentType && contentType.startsWith('image/')
}

const isVideo = (contentType) => {
  return contentType && contentType.startsWith('video/')
}
</script>

<style scoped>
.attachments {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
  color: #409eff;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #999;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.text-muted {
  color: #999;
}

.upload-demo {
  width: 100%;
}
</style>
