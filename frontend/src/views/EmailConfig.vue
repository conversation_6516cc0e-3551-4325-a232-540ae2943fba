<template>
  <div class="email-config">
    <div class="page-header">
      <h2>邮箱配置管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新建配置
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索配置名称或邮箱地址"
            clearable
            @keyup.enter="loadEmailConfigs"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.protocolType" placeholder="协议类型" clearable>
            <el-option label="SMTP" value="SMTP" />
            <el-option label="POP3" value="POP3" />
            <el-option label="IMAP" value="IMAP" />
            <el-option label="Exchange" value="EXCHANGE" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadEmailConfigs">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 邮箱配置列表 -->
    <el-card>
      <el-table
        :data="emailConfigs"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="configName" label="配置名称" min-width="120" />
        <el-table-column prop="emailAddress" label="邮箱地址" min-width="180" />
        <el-table-column prop="protocolType" label="协议类型" width="100" />
        <el-table-column prop="smtpHost" label="SMTP服务器" min-width="150" />
        <el-table-column prop="smtpPort" label="端口" width="80" />
        <el-table-column label="SSL/TLS" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.sslEnabled" type="success" size="small">SSL</el-tag>
            <el-tag v-if="scope.row.tlsEnabled" type="info" size="small">TLS</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'" size="small">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="editConfig(scope.row)">编辑</el-button>
            <el-button size="small" @click="testConnection(scope.row)">测试</el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteConfig(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配置名称" prop="configName">
              <el-input v-model="form.configName" placeholder="请输入配置名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="emailAddress">
              <el-input v-model="form.emailAddress" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="协议类型" prop="protocolType">
          <el-select v-model="form.protocolType" style="width: 100%">
            <el-option label="SMTP" value="SMTP" />
            <el-option label="POP3" value="POP3" />
            <el-option label="IMAP" value="IMAP" />
            <el-option label="Exchange" value="EXCHANGE" />
          </el-select>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="SMTP服务器" prop="smtpHost">
              <el-input v-model="form.smtpHost" placeholder="如：smtp.gmail.com" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SMTP端口" prop="smtpPort">
              <el-input-number v-model="form.smtpPort" :min="1" :max="65535" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="通常是邮箱地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码/授权码" prop="password">
              <el-input v-model="form.password" type="password" placeholder="请输入密码或授权码" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用SSL">
              <el-switch v-model="form.sslEnabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用TLS">
              <el-switch v-model="form.tlsEnabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用配置">
              <el-switch v-model="form.enabled" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('新建配置')
const emailConfigs = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  protocolType: ''
})

// 表单数据
const form = reactive({
  id: null,
  configName: '',
  emailAddress: '',
  protocolType: 'SMTP',
  smtpHost: '',
  smtpPort: 587,
  username: '',
  password: '',
  sslEnabled: true,
  tlsEnabled: false,
  enabled: true,
  remark: ''
})

// 表单验证规则
const formRules = {
  configName: [
    { required: true, message: '请输入配置名称', trigger: 'blur' }
  ],
  emailAddress: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  protocolType: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  smtpHost: [
    { required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '请输入SMTP端口', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码或授权码', trigger: 'blur' }
  ]
}

const formRef = ref()

// 生命周期
onMounted(() => {
  loadEmailConfigs()
})

// 方法
const loadEmailConfigs = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    emailConfigs.value = [
      {
        id: 1,
        configName: 'Gmail配置',
        emailAddress: '<EMAIL>',
        protocolType: 'SMTP',
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        username: '<EMAIL>',
        sslEnabled: true,
        tlsEnabled: false,
        enabled: true,
        remark: 'Gmail邮箱配置'
      },
      {
        id: 2,
        configName: '企业邮箱',
        emailAddress: '<EMAIL>',
        protocolType: 'SMTP',
        smtpHost: 'smtp.company.com',
        smtpPort: 465,
        username: '<EMAIL>',
        sslEnabled: true,
        tlsEnabled: false,
        enabled: true,
        remark: '公司企业邮箱'
      }
    ]
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  dialogTitle.value = '新建配置'
  resetForm()
  dialogVisible.value = true
}

const editConfig = (config) => {
  dialogTitle.value = '编辑配置'
  Object.assign(form, config)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    configName: '',
    emailAddress: '',
    protocolType: 'SMTP',
    smtpHost: '',
    smtpPort: 587,
    username: '',
    password: '',
    sslEnabled: true,
    tlsEnabled: false,
    enabled: true,
    remark: ''
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        ElMessage.success(form.id ? '更新成功' : '创建成功')
        dialogVisible.value = false
        loadEmailConfigs()
      } catch (error) {
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const testConnection = async (config) => {
  try {
    ElMessage.info('正在测试连接...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(`确定要删除配置"${config.configName}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('删除成功')
    loadEmailConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.protocolType = ''
  loadEmailConfigs()
}
</script>

<style scoped>
.email-config {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.search-card {
  margin-bottom: 20px;
}
</style>
