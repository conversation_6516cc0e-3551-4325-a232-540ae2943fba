<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.successCount || 0 }}</div>
              <div class="stats-label">发送成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingCount || 0 }}</div>
              <div class="stats-label">待发送</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.failedCount || 0 }}</div>
              <div class="stats-label">发送失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon info">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.activePlansCount || 0 }}</div>
              <div class="stats-label">活跃计划</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和列表 -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>发送趋势</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon><TrendCharts /></el-icon>
              <p>发送趋势图表</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最近发送记录</span>
          </template>
          <div class="recent-records">
            <el-table :data="recentRecords" style="width: 100%" max-height="300">
              <el-table-column prop="subject" label="邮件主题" show-overflow-tooltip />
              <el-table-column prop="recipient" label="收件人" width="120" />
              <el-table-column prop="status" label="状态" width="80">
                <template #default="scope">
                  <el-tag
                    :type="getStatusType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sendTime" label="发送时间" width="120" />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" class="status-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>系统状态</span>
          </template>
          <div class="system-status">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">邮箱配置：</span>
                  <el-tag type="success" size="small">{{ stats.emailConfigCount || 0 }} 个</el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">收件人：</span>
                  <el-tag type="info" size="small">{{ stats.recipientCount || 0 }} 个</el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">系统状态：</span>
                  <el-tag type="success" size="small">运行正常</el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const stats = ref({
  successCount: 0,
  pendingCount: 0,
  failedCount: 0,
  activePlansCount: 0,
  emailConfigCount: 0,
  recipientCount: 0
})

const recentRecords = ref([])

onMounted(() => {
  loadDashboardData()
})

const loadDashboardData = async () => {
  // 模拟数据，实际应该调用API
  stats.value = {
    successCount: 156,
    pendingCount: 23,
    failedCount: 8,
    activePlansCount: 5,
    emailConfigCount: 3,
    recipientCount: 45
  }

  recentRecords.value = [
    {
      subject: '产品推广邮件',
      recipient: '<EMAIL>',
      status: 'SUCCESS',
      sendTime: '2024-01-15 10:30'
    },
    {
      subject: '活动通知',
      recipient: '<EMAIL>',
      status: 'PENDING',
      sendTime: '2024-01-15 10:25'
    },
    {
      subject: '系统更新通知',
      recipient: '<EMAIL>',
      status: 'FAILED',
      sendTime: '2024-01-15 10:20'
    }
  ]
}

const getStatusType = (status) => {
  const statusMap = {
    'SUCCESS': 'success',
    'PENDING': 'warning',
    'SENDING': 'info',
    'FAILED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'SUCCESS': '成功',
    'PENDING': '待发送',
    'SENDING': '发送中',
    'FAILED': '失败'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stats-icon.success {
  background-color: #67c23a;
}

.stats-icon.warning {
  background-color: #e6a23c;
}

.stats-icon.danger {
  background-color: #f56c6c;
}

.stats-icon.info {
  background-color: #409eff;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.content-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ccc;
  font-size: 48px;
}

.chart-placeholder p {
  margin-top: 10px;
  font-size: 14px;
}

.recent-records {
  height: 300px;
}

.status-row {
  margin-bottom: 20px;
}

.system-status {
  padding: 20px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-label {
  margin-right: 10px;
  color: #666;
}
</style>
