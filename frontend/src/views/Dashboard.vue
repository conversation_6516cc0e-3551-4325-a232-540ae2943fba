<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon success">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.successCount || 0 }}</div>
              <div class="stats-label">发送成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pendingCount || 0 }}</div>
              <div class="stats-label">待发送</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.failedCount || 0 }}</div>
              <div class="stats-label">发送失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon info">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.activePlansCount || 0 }}</div>
              <div class="stats-label">活跃计划</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和列表 -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>发送趋势</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon><TrendCharts /></el-icon>
              <p>发送趋势图表</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>邮件计划统计</span>
              <el-button type="primary" size="small" @click="refreshStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          <div class="mail-plans-stats">
            <el-table :data="mailPlanStats" style="width: 100%" max-height="300">
              <el-table-column prop="planName" label="计划名称" min-width="120" show-overflow-tooltip />
              <el-table-column label="已投" width="60" align="center">
                <template #default="scope">
                  <el-tag type="success" size="small">{{ scope.row.sentCount || 0 }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="未投" width="60" align="center">
                <template #default="scope">
                  <el-tag type="warning" size="small">{{ scope.row.pendingCount || 0 }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="自动回复" width="80" align="center">
                <template #default="scope">
                  <el-tag type="info" size="small">{{ scope.row.autoReplyCount || 0 }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="手工回复" width="80" align="center">
                <template #default="scope">
                  <el-tag type="primary" size="small">{{ scope.row.manualReplyCount || 0 }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="回复率" width="80" align="center">
                <template #default="scope">
                  <span v-if="scope.row.sentCount > 0">
                    {{ calculateReplyRate(scope.row) }}%
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="scope">
                  <el-tag :type="getPlanStatusType(scope.row.status)" size="small">
                    {{ getPlanStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" class="status-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>系统状态</span>
          </template>
          <div class="system-status">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">邮箱配置：</span>
                  <el-tag type="success" size="small">{{ stats.emailConfigCount || 0 }} 个</el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">收件人：</span>
                  <el-tag type="info" size="small">{{ stats.recipientCount || 0 }} 个</el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="status-item">
                  <span class="status-label">系统状态：</span>
                  <el-tag type="success" size="small">运行正常</el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const stats = ref({
  successCount: 0,
  pendingCount: 0,
  failedCount: 0,
  activePlansCount: 0,
  emailConfigCount: 0,
  recipientCount: 0
})

const mailPlanStats = ref([])

onMounted(() => {
  loadDashboardData()
})

const loadDashboardData = async () => {
  // 模拟数据，实际应该调用API
  stats.value = {
    successCount: 156,
    pendingCount: 23,
    failedCount: 8,
    activePlansCount: 5,
    emailConfigCount: 3,
    recipientCount: 45
  }

  mailPlanStats.value = [
    {
      id: 1,
      planName: '产品推广计划',
      sentCount: 156,
      pendingCount: 23,
      autoReplyCount: 12,
      manualReplyCount: 8,
      status: 'ACTIVE'
    },
    {
      id: 2,
      planName: '客户回访计划',
      sentCount: 89,
      pendingCount: 45,
      autoReplyCount: 5,
      manualReplyCount: 15,
      status: 'PAUSED'
    },
    {
      id: 3,
      planName: '新用户欢迎',
      sentCount: 234,
      pendingCount: 12,
      autoReplyCount: 18,
      manualReplyCount: 25,
      status: 'ACTIVE'
    }
  ]
}

const refreshStats = async () => {
  try {
    // 模拟刷新统计数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    loadDashboardData()
    ElMessage.success('统计数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

const getStatusType = (status) => {
  const statusMap = {
    'SUCCESS': 'success',
    'PENDING': 'warning',
    'SENDING': 'info',
    'FAILED': 'danger'
  }
  return statusMap[status] || 'info'
}

const calculateReplyRate = (plan) => {
  if (plan.sentCount === 0) return 0
  const totalReplies = (plan.autoReplyCount || 0) + (plan.manualReplyCount || 0)
  return ((totalReplies / plan.sentCount) * 100).toFixed(1)
}

const getPlanStatusType = (status) => {
  const statusMap = {
    'DRAFT': 'info',
    'ACTIVE': 'success',
    'PAUSED': 'warning',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getPlanStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'ACTIVE': '活跃',
    'PAUSED': '暂停',
    'COMPLETED': '完成',
    'CANCELLED': '取消'
  }
  return statusMap[status] || '未知'
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stats-icon.success {
  background-color: #67c23a;
}

.stats-icon.warning {
  background-color: #e6a23c;
}

.stats-icon.danger {
  background-color: #f56c6c;
}

.stats-icon.info {
  background-color: #409eff;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.content-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ccc;
  font-size: 48px;
}

.chart-placeholder p {
  margin-top: 10px;
  font-size: 14px;
}

.recent-records {
  height: 300px;
}

.status-row {
  margin-bottom: 20px;
}

.system-status {
  padding: 20px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-label {
  margin-right: 10px;
  color: #666;
}
</style>
