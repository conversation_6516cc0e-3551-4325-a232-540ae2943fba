{"name": "auto-send-mail-frontend", "version": "1.0.0", "description": "自动发送邮件系统前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "unplugin-vue-components": "^0.25.2", "unplugin-auto-import": "^0.16.7", "@types/node": "^20.9.0", "sass": "^1.69.5"}}