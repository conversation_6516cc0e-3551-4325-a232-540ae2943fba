// CSS3 var
@use 'common/var' as *;
@use 'mixins/var' as *;
@use 'mixins/mixins' as *;

// for better performance do not dynamically change the root variable if you really
// do not need that, since this could introduce recalculation overhead for rendering.
// https://lisilinhart.info/posts/css-variables-performance/

// common
:root {
  @include set-css-var-value('color-white', $color-white);
  @include set-css-var-value('color-black', $color-black);

  // get rgb
  @each $type in (primary, success, warning, danger, error, info) {
    @include set-css-color-rgb($type);
  }

  // Typography
  @include set-component-css-var('font-size', $font-size);
  @include set-component-css-var('font-family', $font-family);

  @include set-css-var-value('font-weight-primary', 500);
  @include set-css-var-value('font-line-height-primary', 24px);

  // z-index --el-index-#{$type}
  @include set-component-css-var('index', $z-index);

  // --el-border-radius-#{$type}
  @include set-component-css-var('border-radius', $border-radius);

  // Transition
  // refer to this website to get the bezier motion function detail
  // https://cubic-bezier.com/#p1,p2,p3,p4 (change px as your function parameter)
  @include set-component-css-var('transition-duration', $transition-duration);

  @include set-component-css-var('transition-function', $transition-function);
  @include set-component-css-var('transition', $transition);

  // common component size
  @include set-component-css-var('component-size', $common-component-size);
}

// for light
:root {
  color-scheme: light;

  // --el-color-#{$type}
  // --el-color-#{$type}-light-{$i}
  @each $type in (primary, success, warning, danger, error, info) {
    @include set-css-color-type($colors, $type);
  }

  // color-scheme
  // Background --el-bg-color-#{$type}
  @include set-component-css-var('bg-color', $bg-color);
  // --el-text-color-#{$type}
  @include set-component-css-var('text-color', $text-color);
  // --el-border-color-#{$type}
  @include set-component-css-var('border-color', $border-color);
  // Fill --el-fill-color-#{$type}
  @include set-component-css-var('fill-color', $fill-color);

  // Box-shadow
  // --el-box-shadow-#{$type}
  @include set-component-css-var('box-shadow', $box-shadow);
  // Disable base
  @include set-component-css-var('disabled', $disabled);

  // overlay & mask
  @include set-component-css-var('overlay-color', $overlay-color);
  @include set-component-css-var('mask-color', $mask-color);

  // Border
  @include set-css-var-value('border-width', $border-width);
  @include set-css-var-value('border-style', $border-style);
  @include set-css-var-value('border-color-hover', $border-color-hover);
  @include set-css-var-value(
    'border',
    getCssVar('border-width') getCssVar('border-style')
      getCssVar('border-color')
  );

  // Svg
  @include css-var-from-global('svg-monochrome-grey', 'border-color');
}
