@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(notification) {
  @include set-component-css-var('notification', $notification);
}

@include b(notification) {
  display: flex;
  width: getCssVar('notification-width');
  padding: getCssVar('notification-padding');
  border-radius: getCssVar('notification-radius');
  box-sizing: border-box;
  border: 1px solid getCssVar('notification-border-color');
  position: fixed;
  background-color: getCssVar('bg-color', 'overlay');
  box-shadow: getCssVar('notification-shadow');
  transition: opacity getCssVar('transition-duration'),
    transform getCssVar('transition-duration'),
    left getCssVar('transition-duration'),
    right getCssVar('transition-duration'), top 0.4s,
    bottom getCssVar('transition-duration');
  overflow-wrap: break-word;
  overflow: hidden;
  z-index: 9999;

  &.right {
    right: 16px;
  }

  &.left {
    left: 16px;
  }

  @include e(group) {
    flex: 1;
    min-width: 0;
    margin-left: getCssVar('notification-group-margin-left');
    margin-right: getCssVar('notification-group-margin-right');
  }

  @include e(title) {
    font-weight: bold;
    font-size: getCssVar('notification-title-font-size');
    line-height: getCssVar('notification-icon-size');
    color: getCssVar('notification-title-color');
    margin: 0;
  }

  @include e(content) {
    font-size: getCssVar('notification-content-font-size');
    line-height: 24px;
    margin: 6px 0 0;
    color: getCssVar('notification-content-color');

    p {
      margin: 0;
    }
  }

  & .#{$namespace}-notification__icon {
    flex-shrink: 0;
    height: getCssVar('notification-icon-size');
    width: getCssVar('notification-icon-size');
    font-size: getCssVar('notification-icon-size');
  }

  & .#{$namespace}-notification__closeBtn {
    position: absolute;
    top: 18px;
    right: 15px;
    cursor: pointer;
    color: getCssVar('notification-close-color');
    font-size: getCssVar('notification-close-font-size');

    &:hover {
      color: getCssVar('notification-close-hover-color');
    }
  }

  @each $type in (primary, success, info, warning, error) {
    & .#{$namespace}-notification--#{$type} {
      @include css-var-from-global(
        ('notification', 'icon-color'),
        ('color', $type)
      );
      color: getCssVar('notification-icon-color');
    }
  }
}

.#{$namespace}-notification-fade-enter-from {
  &.right {
    right: 0;
    transform: translateX(100%);
  }

  &.left {
    left: 0;
    transform: translateX(-100%);
  }
}

.#{$namespace}-notification-fade-leave-to {
  opacity: 0;
}
