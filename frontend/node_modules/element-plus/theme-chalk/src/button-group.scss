@use 'sass:map';

@use 'common/var' as *;
@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;

@include b(button-group) {
  display: inline-block;
  vertical-align: middle;
  @include utils-clearfix;

  & > .#{$namespace}-button {
    float: left;
    position: relative;
    & + .#{$namespace}-button {
      margin-left: 0;
    }
    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    &:first-child:last-child {
      border-top-right-radius: map.get($button-border-radius, 'default');
      border-bottom-right-radius: map.get($button-border-radius, 'default');
      border-top-left-radius: map.get($button-border-radius, 'default');
      border-bottom-left-radius: map.get($button-border-radius, 'default');

      &.is-round {
        border-radius: getCssVar('border-radius', 'round');
      }

      &.is-circle {
        border-radius: 50%;
      }
    }
    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
    &:not(:last-child) {
      margin-right: -1px;
    }

    &:hover,
    &:focus,
    &:active {
      z-index: 1;
    }

    @include when(active) {
      z-index: 1;
    }
  }

  & > .#{$namespace}-dropdown {
    & > .#{$namespace}-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left-color: getCssVar('button', 'divide-border-color');
    }
  }

  @each $type in (primary, success, warning, danger, info) {
    .#{$namespace}-button--#{$type} {
      &:first-child {
        border-right-color: getCssVar('button', 'divide-border-color');
      }
      &:last-child {
        border-left-color: getCssVar('button', 'divide-border-color');
      }
      &:not(:first-child):not(:last-child) {
        border-left-color: getCssVar('button', 'divide-border-color');
        border-right-color: getCssVar('button', 'divide-border-color');
      }
    }
  }
}
