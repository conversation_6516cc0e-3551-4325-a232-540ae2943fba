@use 'sass:math';

@use 'common/var' as *;
@use 'mixins/mixins' as *;
@use 'mixins/_col' as *;

[class*='#{$namespace}-col-'] {
  box-sizing: border-box;
  @include when(guttered) {
    display: block;
    min-height: 1px;
  }
}

@for $i from 0 through 24 {
  .#{$namespace}-col-#{$i} {
    display: if($i == 0, none, block);
    max-width: (math.div(1, 24) * $i * 100) * 1%;
    flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
    @include when(guttered) {
      display: if($i == 0, none, block);
    }
  }

  .#{$namespace}-col-offset-#{$i} {
    margin-left: (math.div(1, 24) * $i * 100) * 1%;
  }

  .#{$namespace}-col-pull-#{$i} {
    position: relative;
    right: (math.div(1, 24) * $i * 100) * 1%;
  }

  .#{$namespace}-col-push-#{$i} {
    position: relative;
    left: (math.div(1, 24) * $i * 100) * 1%;
  }
}

@include col-size(xs);

@include col-size(sm);

@include col-size(md);

@include col-size(lg);

@include col-size(xl);
