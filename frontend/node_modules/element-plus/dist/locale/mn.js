/*! Element Plus v2.10.5 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleMn = factory());
})(this, (function () { 'use strict';

  var mn = {
    name: "mn",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "\u0422\u0438\u0439\u043C",
        clear: "\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445"
      },
      datepicker: {
        now: "\u041E\u0434\u043E\u043E",
        today: "\u04E8\u043D\u04E9\u04E9\u0434\u04E9\u0440",
        cancel: "\u0411\u043E\u043B\u0438\u0445",
        clear: "\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",
        confirm: "\u0422\u0438\u0439\u043C",
        selectDate: "\u041E\u0433\u043D\u043E\u043E\u0433 \u0441\u043E\u043D\u0433\u043E",
        selectTime: "\u0426\u0430\u0433\u0438\u0439\u0433 \u0441\u043E\u043D\u0433\u043E",
        startDate: "\u042D\u0445\u043B\u044D\u0445 \u043E\u0433\u043D\u043E\u043E",
        startTime: "\u042D\u0445\u043B\u044D\u0445 \u0446\u0430\u0433",
        endDate: "\u0414\u0443\u0443\u0441\u0430\u0445 \u043E\u0433\u043D\u043E\u043E",
        endTime: "\u0414\u0443\u0443\u0441\u0430\u0445 \u0446\u0430\u0433",
        prevYear: "\u04E8\u043C\u043D\u04E9\u0445 \u0436\u0438\u043B",
        nextYear: "\u0414\u0430\u0440\u0430\u0430 \u0436\u0438\u043B",
        prevMonth: "\u04E8\u043C\u043D\u04E9\u0445 \u0441\u0430\u0440",
        nextMonth: "\u0414\u0430\u0440\u0430\u0430 \u0441\u0430\u0440",
        year: "\u043E\u043D",
        month1: "1 \u0441\u0430\u0440",
        month2: "2 \u0441\u0430\u0440",
        month3: "3 \u0441\u0430\u0440",
        month4: "4 \u0441\u0430\u0440",
        month5: "5 \u0441\u0430\u0440",
        month6: "6 \u0441\u0430\u0440",
        month7: "7 \u0441\u0430\u0440",
        month8: "8 \u0441\u0430\u0440",
        month9: "9 \u0441\u0430\u0440",
        month10: "10 \u0441\u0430\u0440",
        month11: "11 \u0441\u0430\u0440",
        month12: "12 \u0441\u0430\u0440",
        week: "\u0414\u043E\u043B\u043E\u043E \u0445\u043E\u043D\u043E\u0433",
        weeks: {
          sun: "\u041D\u044F\u043C",
          mon: "\u0414\u0430\u0432",
          tue: "\u041C\u044F\u0433",
          wed: "\u041B\u0445\u0430",
          thu: "\u041F\u04AF\u0440",
          fri: "\u0411\u0430\u0430",
          sat: "\u0411\u044F\u043C"
        },
        months: {
          jan: "1 \u0441\u0430\u0440",
          feb: "2 \u0441\u0430\u0440",
          mar: "3 \u0441\u0430\u0440",
          apr: "4 \u0441\u0430\u0440",
          may: "5 \u0441\u0430\u0440",
          jun: "6 \u0441\u0430\u0440",
          jul: "7 \u0441\u0430\u0440",
          aug: "8 \u0441\u0430\u0440",
          sep: "9 \u0441\u0430\u0440",
          oct: "10 \u0441\u0430\u0440",
          nov: "11 \u0441\u0430\u0440",
          dec: "12 \u0441\u0430\u0440"
        }
      },
      select: {
        loading: "\u0410\u0447\u0430\u0430\u043B\u0436 \u0431\u0430\u0439\u043D\u0430",
        noMatch: "\u0422\u043E\u0445\u0438\u0440\u043E\u0445 \u04E9\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        noData: "\u04E8\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        placeholder: "\u0421\u043E\u043D\u0433\u043E\u0445"
      },
      mention: {
        loading: "\u0410\u0447\u0430\u0430\u043B\u0436 \u0431\u0430\u0439\u043D\u0430"
      },
      cascader: {
        noMatch: "\u0422\u043E\u0445\u0438\u0440\u043E\u0445 \u04E9\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        loading: "\u0410\u0447\u0430\u0430\u043B\u0436 \u0431\u0430\u0439\u043D\u0430",
        placeholder: "\u0421\u043E\u043D\u0433\u043E\u0445",
        noData: "\u04E8\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439"
      },
      pagination: {
        goto: "\u041E\u0447\u0438\u0445",
        pagesize: "/\u0445\u0443\u0443\u0434\u0430\u0441",
        total: "\u041D\u0438\u0439\u0442 {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u0417\u0443\u0440\u0432\u0430\u0441",
        confirm: "\u0422\u0438\u0439\u043C",
        cancel: "\u0411\u043E\u043B\u0438\u0445",
        error: "\u0411\u0443\u0440\u0443\u0443 \u0443\u0442\u0433\u0430"
      },
      upload: {
        deleteTip: "\u0423\u0441\u0442\u0433\u0430\u0445\u044B\u043D \u0434\u0430\u0440\u0436 \u0430\u0440\u0438\u043B\u0433\u0430",
        delete: "\u0423\u0441\u0442\u0433\u0430\u0445",
        preview: "\u04E8\u043C\u043D\u04E9\u0445",
        continue: "\u04AE\u0440\u0433\u044D\u043B\u0436\u043B\u04AF\u04AF\u043B\u044D\u0445"
      },
      table: {
        emptyText: "\u04E8\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        confirmFilter: "\u0417\u04E9\u0432\u0448\u04E9\u04E9\u0440\u04E9\u0445",
        resetFilter: "\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",
        clearFilter: "\u0411\u04AF\u0433\u0434",
        sumText: "\u041D\u0438\u0439\u0442"
      },
      tree: {
        emptyText: "\u04E8\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439"
      },
      transfer: {
        noMatch: "\u0422\u043E\u0445\u0438\u0440\u043E\u0445 \u04E9\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        noData: "\u04E8\u0433\u04E9\u0433\u0434\u04E9\u043B \u0431\u0430\u0439\u0445\u0433\u04AF\u0439",
        titles: ["\u0416\u0430\u0433\u0441\u0430\u0430\u043B\u0442 1", "\u0416\u0430\u0433\u0441\u0430\u0430\u043B\u0442 2"],
        filterPlaceholder: "\u0423\u0442\u0433\u0430 \u043E\u0440\u0443\u0443\u043B",
        noCheckedFormat: "{total} \u04E9\u0433\u04E9\u0433\u0434\u04E9\u043B",
        hasCheckedFormat: "{checked}/{total} \u0441\u043E\u043D\u0433\u043E\u0441\u043E\u043D"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return mn;

}));
