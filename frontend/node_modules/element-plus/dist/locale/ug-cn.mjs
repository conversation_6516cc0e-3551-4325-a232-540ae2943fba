/*! Element Plus v2.10.5 */

var ugCn = {
  name: "ug-cn",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "\u062C\u06D5\u0632\u0645\u0644\u06D5\u0634",
      clear: "\u0642\u06C7\u0631\u06C7\u0642\u062F\u0627\u0634"
    },
    datepicker: {
      now: "\u06BE\u0627\u0632\u0649\u0631\u0642\u0649 \u06CB\u0627\u0642\u0649\u062A",
      today: "\u0628\u06C8\u06AF\u06C8\u0646",
      cancel: "\u0628\u0649\u0643\u0627\u0631 \u0642\u0649\u0644\u0649\u0634",
      clear: "\u0642\u06C7\u0631\u06C7\u0642\u062F\u0627\u0634",
      confirm: "\u062C\u06D5\u0632\u0645\u0644\u06D5\u0634",
      selectDate: "\u0686\u0649\u0633\u0644\u0627 \u062A\u0627\u0644\u0644\u0627\u06AD",
      selectTime: "\u06CB\u0627\u0642\u0649\u062A \u062A\u0627\u0644\u0644\u0627\u06AD",
      startDate: "\u0628\u0627\u0634\u0644\u0627\u0646\u063A\u0627\u0646 \u0686\u0649\u0633\u0644\u0627",
      startTime: "\u0628\u0627\u0634\u0644\u0627\u0646\u063A\u0627\u0646 \u06CB\u0627\u0642\u0649\u062A",
      endDate: "\u0626\u0627\u062E\u0649\u0631\u0644\u0627\u0634\u0642\u0627\u0646 \u0686\u0649\u0633\u0644\u0627",
      endTime: "\u0626\u0627\u062E\u0649\u0631\u0644\u0627\u0634\u0642\u0627\u0646 \u06CB\u0627\u0642\u0649\u062A",
      prevYear: "\u0626\u0627\u0644\u062F\u0649\u0646\u0642\u0649 \u064A\u0649\u0644",
      nextYear: "\u0643\u0649\u064A\u0649\u0646\u0643\u0649 \u064A\u0649\u0644",
      prevMonth: "\u0626\u0627\u0644\u062F\u0649\u0646\u0642\u0649 \u0626\u0627\u064A",
      nextMonth: "\u0643\u0649\u064A\u0649\u0646\u0643\u0649 \u0626\u0627\u064A",
      year: "- \u064A\u0649\u0644",
      month1: "1-\u0626\u0627\u064A",
      month2: "2-\u0626\u0627\u064A",
      month3: "3-\u0626\u0627\u064A",
      month4: "4-\u0626\u0627\u064A",
      month5: "5-\u0626\u0627\u064A",
      month6: "6-\u0626\u0627\u064A",
      month7: "7-\u0626\u0627\u064A",
      month8: "8-\u0626\u0627\u064A",
      month9: "9-\u0626\u0627\u064A",
      month10: "10-\u0626\u0627\u064A",
      month11: "11-\u0626\u0627\u064A",
      month12: "12-\u0626\u0627\u064A",
      weeks: {
        sun: "\u064A\u06D5\u0643\u0634\u06D5\u0646\u0628\u06D5",
        mon: "\u062F\u06C8\u0634\u06D5\u0646\u0628\u06D5",
        tue: "\u0633\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5",
        wed: "\u0686\u0627\u0631\u0634\u06D5\u0646\u0628\u06D5",
        thu: "\u067E\u06D5\u064A\u0634\u06D5\u0646\u0628\u06D5",
        fri: "\u062C\u06C8\u0645\u06D5",
        sat: "\u0634\u06D5\u0646\u0628\u06D5"
      },
      months: {
        jan: "1-\u0626\u0627\u064A",
        feb: "2-\u0626\u0627\u064A",
        mar: "3-\u0626\u0627\u064A",
        apr: "4-\u0626\u0627\u064A",
        may: "5-\u0626\u0627\u064A",
        jun: "6-\u0626\u0627\u064A",
        jul: "7-\u0626\u0627\u064A",
        aug: "8-\u0626\u0627\u064A",
        sep: "9-\u0626\u0627\u064A",
        oct: "10-\u0626\u0627\u064A",
        nov: "11-\u0626\u0627\u064A",
        dec: "12-\u0626\u0627\u064A"
      }
    },
    select: {
      loading: "\u064A\u06C8\u0643\u0644\u0649\u0646\u0649\u06CB\u0627\u062A\u0649\u062F\u06C7",
      noMatch: "\u0626\u06C7\u0686\u06C7\u0631 \u062A\u06D0\u067E\u0649\u0644\u0645\u0649\u062F\u0649",
      noData: "\u0626\u06C7\u0686\u06C7\u0631 \u064A\u0648\u0642",
      placeholder: "\u062A\u0627\u0644\u0644\u0627\u06AD"
    },
    mention: {
      loading: "\u064A\u06C8\u0643\u0644\u0649\u0646\u0649\u06CB\u0627\u062A\u0649\u062F\u06C7"
    },
    cascader: {
      noMatch: "\u0626\u06C7\u0686\u06C7\u0631 \u062A\u06D0\u067E\u0649\u0644\u0645\u0649\u062F\u0649",
      loading: "\u064A\u06C8\u0643\u0644\u0649\u0646\u0649\u06CB\u0627\u062A\u0649\u062F\u06C7",
      placeholder: "\u062A\u0627\u0644\u0644\u0627\u06AD",
      noData: "\u0626\u06C7\u0686\u06C7\u0631 \u064A\u0648\u0642"
    },
    pagination: {
      goto: "\u0643\u0649\u064A\u0649\u0646\u0643\u0649 \u0628\u06D5\u062A",
      pagesize: "\u062A\u0627\u0644/\u0628\u06D5\u062A",
      total: "\u062C\u06D5\u0645\u0626\u0649\u064A {total} \u062A\u0627\u0644",
      pageClassifier: "\u0628\u06D5\u062A",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\u0626\u06D5\u0633\u0643\u06D5\u0631\u062A\u0649\u0634",
      confirm: "\u062C\u06D5\u0632\u0645\u0644\u06D5\u0634",
      cancel: "\u0628\u0649\u0643\u0627\u0631 \u0642\u0649\u0644\u0649\u0634",
      error: "\u0643\u0649\u0631\u06AF\u06C8\u0632\u06AF\u06D5\u0646 \u0626\u06C7\u0686\u06C7\u0631\u0649\u06AD\u0649\u0632\u062F\u0627 \u062E\u0627\u062A\u0627\u0644\u0649\u0642 \u0628\u0627\u0631!"
    },
    upload: {
      deleteTip: "delete \u0643\u06C7\u0646\u067E\u0643\u0649\u0633\u0649\u0646\u0649 \u0628\u06D0\u0633\u0649\u067E \u0626\u06C6\u0686\u06C8\u0631\u06D5\u0644\u06D5\u064A\u0633\u0649\u0632",
      delete: "\u0626\u06C6\u0686\u06C8\u0631\u06C8\u0634",
      preview: "\u0631\u06D5\u0633\u0649\u0645\u0646\u0649 \u0643\u06C6\u0631\u06C8\u0634",
      continue: "\u0631\u06D5\u0633\u0649\u0645 \u064A\u0648\u0644\u0644\u0627\u0634"
    },
    table: {
      emptyText: "\u0626\u06C7\u0686\u06C7\u0631 \u064A\u0648\u0642",
      confirmFilter: "\u0633\u06C8\u0632\u06AF\u06C8\u0686",
      resetFilter: "\u0642\u0627\u064A\u062A\u0627 \u062A\u0648\u0644\u062F\u06C7\u0631\u06C7\u0634",
      clearFilter: "\u06BE\u06D5\u0645\u0645\u06D5",
      sumText: "\u062C\u06D5\u0645\u0626\u0649\u064A"
    },
    tree: {
      emptyText: "\u0626\u06C7\u0686\u06C7\u0631 \u064A\u0648\u0642"
    },
    transfer: {
      noMatch: "\u0626\u06C7\u0686\u06C7\u0631 \u062A\u06D0\u067E\u0649\u0644\u0645\u0649\u062F\u0649",
      noData: "\u0626\u06C7\u0686\u06C7\u0631 \u064A\u0648\u0642",
      titles: ["\u062C\u06D5\u062F\u06CB\u06D5\u0644 1", "\u062C\u06D5\u062F\u06CB\u06D5\u0644 2"],
      filterPlaceholder: "\u0626\u0649\u0632\u062F\u0649\u0645\u06D5\u0643\u0686\u0649 \u0628\u0648\u0644\u063A\u0627\u0646 \u0645\u06D5\u0632\u0645\u06C7\u0646\u0646\u0649 \u0643\u0649\u0631\u06AF\u06C8\u0632\u06C8\u06AD",
      noCheckedFormat: "\u062C\u06D5\u0645\u0626\u0649\u064A {total} \u062A\u06C8\u0631",
      hasCheckedFormat: "\u062A\u0627\u0644\u0644\u0627\u0646\u063A\u0649\u0646\u0649 {checked}/{total} \u062A\u06C8\u0631"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { ugCn as default };
