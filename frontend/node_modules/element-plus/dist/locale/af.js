/*! Element Plus v2.10.5 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleAf = factory());
})(this, (function () { 'use strict';

  var af = {
    name: "af",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "Bevestig",
        clear: "Maak skoon"
      },
      datepicker: {
        now: "Nou",
        today: "Vandag",
        cancel: "Kanselleer",
        clear: "Maak skoon",
        confirm: "Bevestig",
        selectDate: "Kies datum",
        selectTime: "Kies tyd",
        startDate: "Begindatum",
        startTime: "Begintyd",
        endDate: "Einddatum",
        endTime: "Eindtyd",
        prevYear: "Previous Year",
        nextYear: "Next Year",
        prevMonth: "Previous Month",
        nextMonth: "Next Month",
        year: "Jaar",
        month1: "Jan",
        month2: "Feb",
        month3: "Mrt",
        month4: "Apr",
        month5: "Mei",
        month6: "Jun",
        month7: "Jul",
        month8: "Aug",
        month9: "Sep",
        month10: "Okt",
        month11: "Nov",
        month12: "Des",
        weeks: {
          sun: "So",
          mon: "Ma",
          tue: "Di",
          wed: "Wo",
          thu: "Do",
          fri: "Vr",
          sat: "Sa"
        },
        months: {
          jan: "Jan",
          feb: "Feb",
          mar: "Mrt",
          apr: "Apr",
          may: "Mei",
          jun: "Jun",
          jul: "Jul",
          aug: "Aug",
          sep: "Sep",
          oct: "Okt",
          nov: "Nov",
          dec: "Des"
        }
      },
      select: {
        loading: "Laai",
        noMatch: "Geen toepaslike data",
        noData: "Geen data",
        placeholder: "Kies"
      },
      mention: {
        loading: "Laai"
      },
      cascader: {
        noMatch: "Geen toepaslike data",
        loading: "Laai",
        placeholder: "Kies",
        noData: "Geen data"
      },
      pagination: {
        goto: "Gaan na",
        pagesize: "/page",
        total: "Totaal {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "Boodskap",
        confirm: "Bevestig",
        cancel: "Kanselleer",
        error: "Ongeldige invoer"
      },
      upload: {
        deleteTip: "press delete to remove",
        delete: "Verwyder",
        preview: "Voorskou",
        continue: "Gaan voort"
      },
      table: {
        emptyText: "Geen Data",
        confirmFilter: "Bevestig",
        resetFilter: "Herstel",
        clearFilter: "Alles",
        sumText: "Som"
      },
      tree: {
        emptyText: "Geen Data"
      },
      transfer: {
        noMatch: "Geen toepaslike data",
        noData: "Geen data",
        titles: ["Lys 1", "Lys 2"],
        filterPlaceholder: "Voer sleutelwoord in",
        noCheckedFormat: "{total} items",
        hasCheckedFormat: "{checked}/{total} gekies"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return af;

}));
