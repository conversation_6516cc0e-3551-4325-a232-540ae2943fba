{"version": 3, "file": "cascader2.mjs", "sources": ["../../../../../../packages/components/cascader/src/cascader.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    :visible=\"popperVisible\"\n    :teleported=\"teleported\"\n    :popper-class=\"[nsCascader.e('dropdown'), popperClass]\"\n    :popper-options=\"popperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :stop-popper-mouse-event=\"false\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :transition=\"`${nsCascader.namespace.value}-zoom-in-top`\"\n    :effect=\"effect\"\n    pure\n    :persistent=\"persistent\"\n    @hide=\"hideSuggestionPanel\"\n  >\n    <template #default>\n      <div\n        v-clickoutside:[contentRef]=\"() => togglePopperVisible(false)\"\n        :class=\"cascaderKls\"\n        :style=\"cascaderStyle\"\n        @click=\"() => togglePopperVisible(readonly ? undefined : true)\"\n        @keydown=\"handleKeyDown\"\n        @mouseenter=\"inputHover = true\"\n        @mouseleave=\"inputHover = false\"\n      >\n        <el-input\n          ref=\"input\"\n          v-model=\"inputValue\"\n          :placeholder=\"currentPlaceholder\"\n          :readonly=\"readonly\"\n          :disabled=\"isDisabled\"\n          :validate-event=\"false\"\n          :size=\"realSize\"\n          :class=\"inputClass\"\n          :tabindex=\"multiple && filterable && !isDisabled ? -1 : undefined\"\n          @compositionstart=\"handleComposition\"\n          @compositionupdate=\"handleComposition\"\n          @compositionend=\"handleComposition\"\n          @focus=\"handleFocus\"\n          @blur=\"handleBlur\"\n          @input=\"handleInput\"\n        >\n          <template v-if=\"$slots.prefix\" #prefix>\n            <slot name=\"prefix\" />\n          </template>\n          <template #suffix>\n            <el-icon\n              v-if=\"clearBtnVisible\"\n              key=\"clear\"\n              :class=\"[nsInput.e('icon'), 'icon-circle-close']\"\n              @click.stop=\"handleClear\"\n            >\n              <circle-close />\n            </el-icon>\n            <el-icon\n              v-else\n              key=\"arrow-down\"\n              :class=\"cascaderIconKls\"\n              @click.stop=\"togglePopperVisible()\"\n            >\n              <arrow-down />\n            </el-icon>\n          </template>\n        </el-input>\n\n        <div\n          v-if=\"multiple\"\n          ref=\"tagWrapper\"\n          :class=\"[\n            nsCascader.e('tags'),\n            nsCascader.is('validate', Boolean(validateState)),\n          ]\"\n        >\n          <slot name=\"tag\" :data=\"allPresentTags\" :delete-tag=\"deleteTag\">\n            <el-tag\n              v-for=\"tag in presentTags\"\n              :key=\"tag.key\"\n              :type=\"tagType\"\n              :size=\"tagSize\"\n              :effect=\"tagEffect\"\n              :hit=\"tag.hitState\"\n              :closable=\"tag.closable\"\n              disable-transitions\n              @close=\"deleteTag(tag)\"\n            >\n              <template v-if=\"tag.isCollapseTag === false\">\n                <span>{{ tag.text }}</span>\n              </template>\n              <template v-else>\n                <el-tooltip\n                  :disabled=\"popperVisible || !collapseTagsTooltip\"\n                  :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                  placement=\"bottom\"\n                  :effect=\"effect\"\n                >\n                  <template #default>\n                    <span>{{ tag.text }}</span>\n                  </template>\n                  <template #content>\n                    <el-scrollbar :max-height=\"maxCollapseTagsTooltipHeight\">\n                      <div :class=\"nsCascader.e('collapse-tags')\">\n                        <div\n                          v-for=\"(tag2, idx) in allPresentTags.slice(\n                            maxCollapseTags\n                          )\"\n                          :key=\"idx\"\n                          :class=\"nsCascader.e('collapse-tag')\"\n                        >\n                          <el-tag\n                            :key=\"tag2.key\"\n                            class=\"in-tooltip\"\n                            :type=\"tagType\"\n                            :size=\"tagSize\"\n                            :effect=\"tagEffect\"\n                            :hit=\"tag2.hitState\"\n                            :closable=\"tag2.closable\"\n                            disable-transitions\n                            @close=\"deleteTag(tag2)\"\n                          >\n                            <span>{{ tag2.text }}</span>\n                          </el-tag>\n                        </div>\n                      </div>\n                    </el-scrollbar>\n                  </template>\n                </el-tooltip>\n              </template>\n            </el-tag>\n          </slot>\n          <input\n            v-if=\"filterable && !isDisabled\"\n            v-model=\"searchInputValue\"\n            type=\"text\"\n            :class=\"nsCascader.e('search-input')\"\n            :placeholder=\"presentText ? '' : inputPlaceholder\"\n            @input=\"(e) => handleInput(searchInputValue, e as KeyboardEvent)\"\n            @click.stop=\"togglePopperVisible(true)\"\n            @keydown.delete=\"handleDelete\"\n            @compositionstart=\"handleComposition\"\n            @compositionupdate=\"handleComposition\"\n            @compositionend=\"handleComposition\"\n            @focus=\"handleFocus\"\n            @blur=\"handleBlur\"\n          />\n        </div>\n      </div>\n    </template>\n\n    <template #content>\n      <div v-if=\"$slots.header\" :class=\"nsCascader.e('header')\" @click.stop>\n        <slot name=\"header\" />\n      </div>\n      <el-cascader-panel\n        v-show=\"!filtering\"\n        ref=\"cascaderPanelRef\"\n        v-model=\"checkedValue\"\n        :options=\"options\"\n        :props=\"props.props\"\n        :border=\"false\"\n        :render-label=\"$slots.default\"\n        @expand-change=\"handleExpandChange\"\n        @close=\"$nextTick(() => togglePopperVisible(false))\"\n      >\n        <template #empty>\n          <slot name=\"empty\" />\n        </template>\n      </el-cascader-panel>\n      <el-scrollbar\n        v-if=\"filterable\"\n        v-show=\"filtering\"\n        ref=\"suggestionPanel\"\n        tag=\"ul\"\n        :class=\"nsCascader.e('suggestion-panel')\"\n        :view-class=\"nsCascader.e('suggestion-list')\"\n        @keydown=\"handleSuggestionKeyDown\"\n      >\n        <template v-if=\"suggestions.length\">\n          <li\n            v-for=\"item in suggestions\"\n            :key=\"item.uid\"\n            :class=\"[\n              nsCascader.e('suggestion-item'),\n              nsCascader.is('checked', item.checked),\n            ]\"\n            :tabindex=\"-1\"\n            @click=\"handleSuggestionClick(item)\"\n          >\n            <slot name=\"suggestion-item\" :item=\"item\">\n              <span>{{ item.text }}</span>\n              <el-icon v-if=\"item.checked\">\n                <check />\n              </el-icon>\n            </slot>\n          </li>\n        </template>\n        <slot v-else name=\"empty\">\n          <li :class=\"nsCascader.e('empty-text')\">\n            {{ t('el.cascader.noMatch') }}\n          </li>\n        </slot>\n      </el-scrollbar>\n      <div v-if=\"$slots.footer\" :class=\"nsCascader.e('footer')\" @click.stop>\n        <slot name=\"footer\" />\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue'\nimport { cloneDeep, debounce } from 'lodash-unified'\nimport { useCssVar, useResizeObserver } from '@vueuse/core'\nimport {\n  debugWarn,\n  focusNode,\n  getSibling,\n  isClient,\n  isPromise,\n} from '@element-plus/utils'\nimport ElCascaderPanel from '@element-plus/components/cascader-panel'\nimport ElInput from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport {\n  useComposition,\n  useEmptyValues,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { ArrowDown, Check, CircleClose } from '@element-plus/icons-vue'\nimport { cascaderEmits, cascaderProps } from './cascader'\n\nimport type { Options } from '@element-plus/components/popper'\nimport type { ComputedRef, Ref, StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { ScrollbarInstance } from '@element-plus/components/scrollbar'\nimport type {\n  CascaderNode,\n  CascaderPanelInstance,\n  CascaderValue,\n  Tag,\n} from '@element-plus/components/cascader-panel'\n\nconst popperOptions: Partial<Options> = {\n  modifiers: [\n    {\n      name: 'arrowPosition',\n      enabled: true,\n      phase: 'main',\n      fn: ({ state }: any) => {\n        const { modifiersData, placement } = state\n        if (['right', 'left', 'bottom', 'top'].includes(placement)) return\n        if (modifiersData.arrow) {\n          modifiersData.arrow.x = 35\n        }\n      },\n      requires: ['arrow'],\n    },\n  ],\n}\nconst COMPONENT_NAME = 'ElCascader'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(cascaderProps)\nconst emit = defineEmits(cascaderEmits)\nconst attrs = useAttrs()\n\nlet inputInitialHeight = 0\nlet pressDeleteCount = 0\n\nconst nsCascader = useNamespace('cascader')\nconst nsInput = useNamespace('input')\n\nconst { t } = useLocale()\nconst { form, formItem } = useFormItem()\nconst { valueOnClear } = useEmptyValues(props)\nconst { isComposing, handleComposition } = useComposition({\n  afterComposition(event) {\n    const text = (event.target as HTMLInputElement)?.value\n    handleInput(text)\n  },\n})\n\nconst tooltipRef: Ref<TooltipInstance | null> = ref(null)\nconst input: Ref<InputInstance | null> = ref(null)\nconst tagWrapper = ref(null)\nconst cascaderPanelRef: Ref<CascaderPanelInstance | null> = ref(null)\nconst suggestionPanel: Ref<ScrollbarInstance | null> = ref(null)\nconst popperVisible = ref(false)\nconst inputHover = ref(false)\nconst filtering = ref(false)\nconst filterFocus = ref(false)\nconst inputValue = ref('')\nconst searchInputValue = ref('')\nconst presentTags: Ref<Tag[]> = ref([])\nconst allPresentTags: Ref<Tag[]> = ref([])\nconst suggestions: Ref<CascaderNode[]> = ref([])\n\nconst cascaderStyle = computed<StyleValue>(() => {\n  return attrs.style as StyleValue\n})\n\nconst isDisabled = computed(() => props.disabled || form?.disabled)\nconst inputPlaceholder = computed(\n  () => props.placeholder ?? t('el.cascader.placeholder')\n)\nconst currentPlaceholder = computed(() =>\n  searchInputValue.value || presentTags.value.length > 0 || isComposing.value\n    ? ''\n    : inputPlaceholder.value\n)\nconst realSize = useFormSize()\nconst tagSize = computed(() =>\n  realSize.value === 'small' ? 'small' : 'default'\n)\nconst multiple = computed(() => !!props.props.multiple)\nconst readonly = computed(() => !props.filterable || multiple.value)\nconst searchKeyword = computed(() =>\n  multiple.value ? searchInputValue.value : inputValue.value\n)\nconst checkedNodes: ComputedRef<CascaderNode[]> = computed(\n  () => cascaderPanelRef.value?.checkedNodes || []\n)\nconst clearBtnVisible = computed(() => {\n  if (\n    !props.clearable ||\n    isDisabled.value ||\n    filtering.value ||\n    !inputHover.value\n  )\n    return false\n\n  return !!checkedNodes.value.length\n})\nconst presentText = computed(() => {\n  const { showAllLevels, separator } = props\n  const nodes = checkedNodes.value\n  return nodes.length\n    ? multiple.value\n      ? ''\n      : nodes[0].calcText(showAllLevels, separator)\n    : ''\n})\n\nconst validateState = computed(() => formItem?.validateState || '')\n\nconst checkedValue = computed<CascaderValue>({\n  get() {\n    return cloneDeep(props.modelValue) as CascaderValue\n  },\n  set(val) {\n    // https://github.com/element-plus/element-plus/issues/17647\n    const value = val ?? valueOnClear.value\n    emit(UPDATE_MODEL_EVENT, value)\n    emit(CHANGE_EVENT, value)\n    if (props.validateEvent) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  },\n})\n\nconst cascaderKls = computed(() => {\n  return [\n    nsCascader.b(),\n    nsCascader.m(realSize.value),\n    nsCascader.is('disabled', isDisabled.value),\n    attrs.class,\n  ]\n})\n\nconst cascaderIconKls = computed(() => {\n  return [\n    nsInput.e('icon'),\n    'icon-arrow-down',\n    nsCascader.is('reverse', popperVisible.value),\n  ]\n})\n\nconst inputClass = computed(() => {\n  return nsCascader.is('focus', popperVisible.value || filterFocus.value)\n})\n\nconst contentRef = computed(() => {\n  return tooltipRef.value?.popperRef?.contentRef\n})\n\nconst togglePopperVisible = (visible?: boolean) => {\n  if (isDisabled.value) return\n\n  visible = visible ?? !popperVisible.value\n\n  if (visible !== popperVisible.value) {\n    popperVisible.value = visible\n    input.value?.input?.setAttribute('aria-expanded', `${visible}`)\n\n    if (visible) {\n      updatePopperPosition()\n      nextTick(cascaderPanelRef.value?.scrollToExpandingNode)\n    } else if (props.filterable) {\n      syncPresentTextValue()\n    }\n\n    emit('visibleChange', visible)\n  }\n}\n\nconst updatePopperPosition = () => {\n  nextTick(() => {\n    tooltipRef.value?.updatePopper()\n  })\n}\nconst hideSuggestionPanel = () => {\n  filtering.value = false\n}\n\nconst genTag = (node: CascaderNode): Tag => {\n  const { showAllLevels, separator } = props\n  return {\n    node,\n    key: node.uid,\n    text: node.calcText(showAllLevels, separator),\n    hitState: false,\n    closable: !isDisabled.value && !node.isDisabled,\n    isCollapseTag: false,\n  }\n}\n\nconst deleteTag = (tag: Tag) => {\n  const node = tag.node as CascaderNode\n  node.doCheck(false)\n  cascaderPanelRef.value?.calculateCheckedValue()\n  emit('removeTag', node.valueByOption)\n}\n\nconst getStrategyCheckedNodes = (): CascaderNode[] => {\n  switch (props.showCheckedStrategy) {\n    case 'child':\n      return checkedNodes.value\n    case 'parent': {\n      const clickedNodes = getCheckedNodes(false)\n      const clickedNodesValue = clickedNodes!.map((o) => o.value)\n      const parentNodes = clickedNodes!.filter(\n        (o) => !o.parent || !clickedNodesValue.includes(o.parent.value)\n      )\n      return parentNodes\n    }\n    default:\n      return []\n  }\n}\n\nconst calculatePresentTags = () => {\n  if (!multiple.value) return\n\n  const nodes = getStrategyCheckedNodes()\n  const tags: Tag[] = []\n\n  const allTags: Tag[] = []\n  nodes.forEach((node) => allTags.push(genTag(node)))\n  allPresentTags.value = allTags\n\n  if (nodes.length) {\n    nodes\n      .slice(0, props.maxCollapseTags)\n      .forEach((node) => tags.push(genTag(node)))\n    const rest = nodes.slice(props.maxCollapseTags)\n    const restCount = rest.length\n\n    if (restCount) {\n      if (props.collapseTags) {\n        tags.push({\n          key: -1,\n          text: `+ ${restCount}`,\n          closable: false,\n          isCollapseTag: true,\n        })\n      } else {\n        rest.forEach((node) => tags.push(genTag(node)))\n      }\n    }\n  }\n\n  presentTags.value = tags\n}\n\nconst calculateSuggestions = () => {\n  const { filterMethod, showAllLevels, separator } = props\n  const res = cascaderPanelRef.value\n    ?.getFlattedNodes(!props.props.checkStrictly)\n    ?.filter((node) => {\n      if (node.isDisabled) return false\n      node.calcText(showAllLevels, separator)\n      return filterMethod(node, searchKeyword.value)\n    })\n\n  if (multiple.value) {\n    presentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n    allPresentTags.value.forEach((tag) => {\n      tag.hitState = false\n    })\n  }\n\n  filtering.value = true\n  suggestions.value = res!\n  updatePopperPosition()\n}\n\nconst focusFirstNode = () => {\n  let firstNode!: HTMLElement\n\n  if (filtering.value && suggestionPanel.value) {\n    firstNode = suggestionPanel.value.$el.querySelector(\n      `.${nsCascader.e('suggestion-item')}`\n    )\n  } else {\n    firstNode = cascaderPanelRef.value?.$el.querySelector(\n      `.${nsCascader.b('node')}[tabindex=\"-1\"]`\n    )\n  }\n\n  if (firstNode) {\n    firstNode.focus()\n    !filtering.value && firstNode.click()\n  }\n}\n\nconst updateStyle = () => {\n  const inputInner = input.value?.input\n  const tagWrapperEl = tagWrapper.value\n  const suggestionPanelEl = suggestionPanel.value?.$el\n\n  if (!isClient || !inputInner) return\n\n  if (suggestionPanelEl) {\n    const suggestionList = suggestionPanelEl.querySelector(\n      `.${nsCascader.e('suggestion-list')}`\n    )\n    suggestionList.style.minWidth = `${inputInner.offsetWidth}px`\n  }\n\n  if (tagWrapperEl) {\n    const { offsetHeight } = tagWrapperEl\n    // 2 is el-input__wrapper padding\n    const height =\n      presentTags.value.length > 0\n        ? `${Math.max(offsetHeight, inputInitialHeight) - 2}px`\n        : `${inputInitialHeight}px`\n    inputInner.style.height = height\n    updatePopperPosition()\n  }\n}\n\nconst getCheckedNodes = (leafOnly: boolean) => {\n  return cascaderPanelRef.value?.getCheckedNodes(leafOnly)\n}\n\nconst handleExpandChange = (value: CascaderValue) => {\n  updatePopperPosition()\n  emit('expandChange', value)\n}\n\nconst handleKeyDown = (e: KeyboardEvent) => {\n  if (isComposing.value) return\n\n  switch (e.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      togglePopperVisible()\n      break\n    case EVENT_CODE.down:\n      togglePopperVisible(true)\n      nextTick(focusFirstNode)\n      e.preventDefault()\n      break\n    case EVENT_CODE.esc:\n      if (popperVisible.value === true) {\n        e.preventDefault()\n        e.stopPropagation()\n        togglePopperVisible(false)\n      }\n      break\n    case EVENT_CODE.tab:\n      togglePopperVisible(false)\n      break\n  }\n}\n\nconst handleClear = () => {\n  cascaderPanelRef.value?.clearCheckedNodes()\n  if (!popperVisible.value && props.filterable) {\n    syncPresentTextValue()\n  }\n  togglePopperVisible(false)\n  emit('clear')\n}\n\nconst syncPresentTextValue = () => {\n  const { value } = presentText\n  inputValue.value = value\n  searchInputValue.value = value\n}\n\nconst handleSuggestionClick = (node: CascaderNode) => {\n  const { checked } = node\n\n  if (multiple.value) {\n    cascaderPanelRef.value?.handleCheckChange(node, !checked, false)\n  } else {\n    !checked && cascaderPanelRef.value?.handleCheckChange(node, true, false)\n    togglePopperVisible(false)\n  }\n}\n\nconst handleSuggestionKeyDown = (e: KeyboardEvent) => {\n  const target = e.target as HTMLElement\n  const { code } = e\n\n  switch (code) {\n    case EVENT_CODE.up:\n    case EVENT_CODE.down: {\n      e.preventDefault()\n      const distance = code === EVENT_CODE.up ? -1 : 1\n      focusNode(\n        getSibling(\n          target,\n          distance,\n          `.${nsCascader.e('suggestion-item')}[tabindex=\"-1\"]`\n        ) as HTMLElement\n      )\n      break\n    }\n    case EVENT_CODE.enter:\n    case EVENT_CODE.numpadEnter:\n      target.click()\n      break\n  }\n}\n\nconst handleDelete = () => {\n  const tags = presentTags.value\n  const lastTag = tags[tags.length - 1]\n  pressDeleteCount = searchInputValue.value ? 0 : pressDeleteCount + 1\n\n  if (!lastTag || !pressDeleteCount || (props.collapseTags && tags.length > 1))\n    return\n\n  if (lastTag.hitState) {\n    deleteTag(lastTag)\n  } else {\n    lastTag.hitState = true\n  }\n}\n\nconst handleFocus = (e: FocusEvent) => {\n  const el = e.target as HTMLInputElement\n  const name = nsCascader.e('search-input')\n  if (el.className === name) {\n    filterFocus.value = true\n  }\n  emit('focus', e)\n}\n\nconst handleBlur = (e: FocusEvent) => {\n  filterFocus.value = false\n  emit('blur', e)\n}\n\nconst handleFilter = debounce(() => {\n  const { value } = searchKeyword\n\n  if (!value) return\n\n  const passed = props.beforeFilter(value)\n\n  if (isPromise(passed)) {\n    passed.then(calculateSuggestions).catch(() => {\n      /* prevent log error */\n    })\n  } else if (passed !== false) {\n    calculateSuggestions()\n  } else {\n    hideSuggestionPanel()\n  }\n}, props.debounce)\n\nconst handleInput = (val: string, e?: KeyboardEvent) => {\n  !popperVisible.value && togglePopperVisible(true)\n\n  if (e?.isComposing) return\n\n  val ? handleFilter() : hideSuggestionPanel()\n}\n\nconst getInputInnerHeight = (inputInner: HTMLElement): number =>\n  Number.parseFloat(\n    useCssVar(nsInput.cssVarName('input-height'), inputInner).value\n  ) - 2\n\nwatch(filtering, updatePopperPosition)\n\nwatch(\n  [checkedNodes, isDisabled, () => props.collapseTags],\n  calculatePresentTags\n)\n\nwatch(presentTags, () => {\n  nextTick(() => updateStyle())\n})\n\nwatch(realSize, async () => {\n  await nextTick()\n  const inputInner = input.value!.input!\n  inputInitialHeight = getInputInnerHeight(inputInner) || inputInitialHeight\n  updateStyle()\n})\n\nwatch(presentText, syncPresentTextValue, { immediate: true })\n\nonMounted(() => {\n  const inputInner = input.value!.input!\n\n  const inputInnerHeight = getInputInnerHeight(inputInner)\n\n  inputInitialHeight = inputInner.offsetHeight || inputInnerHeight\n  useResizeObserver(inputInner, updateStyle)\n})\n\ndefineExpose({\n  /**\n   * @description get an array of currently selected node,(leafOnly) whether only return the leaf checked nodes, default is `false`\n   */\n  getCheckedNodes,\n  /**\n   * @description cascader panel ref\n   */\n  cascaderPanelRef,\n  /**\n   * @description toggle the visible of popper\n   */\n  togglePopperVisible,\n  /**\n   * @description cascader content ref\n   */\n  contentRef,\n  /**\n   * @description selected content text\n   */\n  presentText,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_withCtx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;mCAkRc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AArBA,IAAA,MAAM,aAAkC,GAAA;AAAA,MACtC,SAAW,EAAA;AAAA,QACT;AAAA,UACE,IAAM,EAAA,eAAA;AAAA,UACN,OAAS,EAAA,IAAA;AAAA,UACT,KAAO,EAAA,MAAA;AAAA,UACP,EAAI,EAAA,CAAC,EAAE,KAAA,EAAiB,KAAA;AACtB,YAAM,MAAA,EAAE,aAAe,EAAA,SAAA,EAAc,GAAA,KAAA,CAAA;AACrC,YAAI,IAAA,CAAC,SAAS,MAAQ,EAAA,QAAA,EAAU,KAAK,CAAE,CAAA,QAAA,CAAS,SAAS,CAAG;AAC5D,cAAA;AACE,YAAA,IAAA,mBAAwB,EAAA;AAAA,cAC1B,aAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AAAA,aACF;AAAA,WACA;AAAkB,UACpB,QAAA,EAAA,CAAA,OAAA,CAAA;AAAA,SACF;AAAA,OACF;AASA,KAAA,CAAA;AAEA,IAAA,MAAyB,KAAA,GAAA,QAAA,EAAA,CAAA;AACzB,IAAA,IAAI,kBAAmB,GAAA,CAAA,CAAA;AAEvB,IAAM,IAAA;AACN,IAAM,MAAA,UAAU,eAAoB,CAAA,UAAA,CAAA,CAAA;AAEpC,IAAM,MAAA,OAAI,GAAc,YAAA,CAAA,OAAA,CAAA,CAAA;AACxB,IAAA,MAAM,EAAE,CAAA,EAAA,GAAM,SAAS,EAAA,CAAA;AACvB,IAAA,MAAM,EAAE,IAAA,EAAA,QAAiB,EAAA,GAAA,WAAA,EAAe,CAAK;AAC7C,IAAA,MAAM,EAAE,YAAA,EAA+B,GAAA,cAAA,CAAA,KAAmB,CAAA,CAAA;AAAA,IAAA,mBACvC,EAAO,iBAAA,EAAA,GAAA,cAAA,CAAA;AACtB,MAAM,sBAA2C,EAAA;AACjD,QAAA,IAAA,EAAA,CAAA;AAAgB,QAClB,MAAA,IAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,QACD,WAAA,CAAA,IAAA,CAAA,CAAA;AAED,OAAM;AACN,KAAM,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAa,IAAI,IAAI,CAAA,CAAA;AAC3B,IAAM,MAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAA,GAAA,CAAA,KAAqD,CAAI;AAC/D,IAAM,MAAA,gBAAgB,MAAS,CAAA,IAAA,CAAA,CAAA;AAC/B,IAAM,MAAA,kBAAsB,GAAA,CAAA,IAAA,CAAA,CAAA;AAC5B,IAAM,MAAA,gBAAgB,GAAK,CAAA,KAAA,CAAA,CAAA;AAC3B,IAAM,MAAA,UAAA,GAAA,SAAuB,CAAA,CAAA;AAC7B,IAAM,MAAA,SAAA,GAAA,SAAmB,CAAA,CAAA;AACzB,IAAM,MAAA,WAAA,GAAA,GAAA,CAAA,KAAuB,CAAE,CAAA;AAC/B,IAAM,MAAA,UAAA,GAAA,GAA8B,CAAA,EAAC,CAAC,CAAA;AACtC,IAAM,MAAA,gBAAA,GAAiC,GAAC,CAAC,EAAA,CAAA,CAAA;AACzC,IAAM,MAAA,WAAA,GAAmC,GAAI,CAAA,EAAE,CAAA,CAAA;AAE/C,IAAM,MAAA,cAAA,WAAqC;AACzC,IAAA,MAAA,WAAa,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACf,MAAC,aAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAA,kBAA4B,CAAA;AAC5B,KAAA,CAAA,CAAA;AAAyB,IAAA,MACjB,UAAqB,GAAA,QAAA,CAAA,MAA2B,KAAA,CAAA,QAAA,KAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IACxD,MAAA,gBAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,IAAM,EAAqB,CAAA;AAAA,MAAS,OACjB,CAAA,EAAA,GAAA,KAAA,CAAA,WAAA,KAAqB,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,yBAAoB,CAAA,CAAY;AAEjD,KACvB,CAAA,CAAA;AACA,IAAA,MAAM,kBAAuB,GAAA,QAAA,CAAA,MAAA,gBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,IAAA,WAAA,CAAA,KAAA,GAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,CAAA,CAAA;AAC7B,IAAA,MAAM,QAAU,GAAA,WAAA,EAAA,CAAA;AAAA,IAAA,MACd,OAAA,GAAA,QAAmB,CAAA,MAAA,QAAoB,CAAA,KAAA,KAAA,OAAA,GAAA,OAAA,GAAA,SAAA,CAAA,CAAA;AAAA,IACzC,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA,IAAA,MAAM,WAAW,QAAS,CAAA,MAAM,CAAC,KAAC,CAAA,UAAoB,IAAA,QAAA,CAAA,KAAA,CAAA,CAAA;AACtD,IAAA,MAAM,aAAoB,GAAA,QAAA,CAAA,MAAa,QAAA,CAAA,KAAA,mBAA4B,CAAA,KAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACnE,IAAA,MAAM,YAAgB,GAAA,QAAA,CAAA,MAAA;AAAA,MAAS,IAC7B,EAAA,CAAA;AAAqD,MACvD,OAAA,CAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,EAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAkD,IAAA,MAC1C,eAAA,GAAA,QAAwB,CAAA,MAAA;AAAiB,MACjD,IAAA,CAAA,KAAA,CAAA,SAAA,IAAA,UAAA,CAAA,KAAA,IAAA,SAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA;AACA,QAAM,OAAA,KAAA,CAAA;AACJ,MACE,qBACA,CAAA,KAAA,CAAA,MAAA,CAAW;AAIX,KAAO,CAAA,CAAA;AAET,IAAO,MAAA,WAAE,GAAA,QAAmB,CAAA,MAAA;AAAA,MAC7B,MAAA,EAAA,aAAA,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AACD,MAAM,MAAA,KAAA,GAAA,aAAuB,KAAM,CAAA;AACjC,MAAM,OAAA,KAAiB,CAAA,MAAA,GAAA,QAAA,CAAA,KAAc,GAAA,EAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,EAAA,SAAA,CAAA,GAAA,EAAA,CAAA;AACrC,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,aACH,GAAA,QAAA,CAAA,MACE,CAAA,QACA,IAAM,IAAG,GAAA,KAAA,CAAA,GAAwB,QAAA,CAAA,aAAS,KAC5C,EAAA,CAAA,CAAA;AAAA,IACN,MAAC,YAAA,GAAA,QAAA,CAAA;AAED,MAAA,GAAA,GAAsB;AAEtB,QAAA,iBAAqB,KAAwB,CAAA,UAAA,CAAA,CAAA;AAAA,OACrC;AACJ,MAAO,GAAA,CAAA,GAAA,EAAA;AAA0B,QACnC,MAAA,KAAA,GAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,YACS,CAAA,kBAAA,EAAA,KAAA,CAAA,CAAA;AAEP,QAAM,IAAA,CAAA,mBAA4B,CAAA,CAAA;AAClC,QAAA,IAAA,qBAAyB;AACzB,UAAA,mBAAwB,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACxB,SAAA;AACE,OAAU;AAAgD,KAC5D,CAAA,CAAA;AAAA,IACF,MAAA,WAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,UAAA,CAAA,CAAA,EAAA;AACJ,QAAO,UAAA,CAAA,CAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,QACL,WAAW,EAAE,CAAA,UAAA,EAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACb,KAAA,CAAA,KAAW;AAAgB,OAAA,CAC3B;AAA0C,KAAA,CAAA,CAAA;AACpC,IACR,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA;AAED,QAAM,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AACJ,QAAO,iBAAA;AAAA,QACL,UAAU,CAAM,EAAA,CAAA,SAAA,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,OAChB,CAAA;AAAA,KAAA,CAAA,CAAA;AAC4C,IAC9C,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA,UAAA,CAAA,EAAA,CAAA,OAAA,EAAA,aAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,qBAAqB,CAAA,MAAuB;AAA0B,MACvE,IAAA,EAAA,EAAA,EAAA,CAAA;AAED,MAAM,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,aAA4B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAChC,KAAO,CAAA,CAAA;AAA6B,IACtC,MAAC,mBAAA,GAAA,CAAA,OAAA,KAAA;AAED,MAAM,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,IAAI,WAAW,KAAO;AAEtB,QAAU,OAAA;AAEV,MAAI,OAAA,GAAA,yBAAiC,GAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACnC,MAAA,IAAA,OAAA,KAAc,aAAQ,CAAA,KAAA,EAAA;AACtB,QAAA,aAAa,CAAO,KAAA,GAAA,OAAA,CAAA;AAEpB,QAAA,CAAA,EAAA,GAAa,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,eAAA,EAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACX,QAAqB,IAAA,OAAA,EAAA;AACrB,UAAS,oBAAA,EAAA,CAAA;AAA6C,UACxD,QAAA,CAAW,MAAM,gBAAY,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,CAAA;AAC3B,SAAqB,MAAA,IAAA,KAAA,CAAA,UAAA,EAAA;AAAA,UACvB,oBAAA,EAAA,CAAA;AAEA,SAAA;AAA6B,QAC/B,IAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,oBAAe,GAAA,MAAA;AACb,MAAA,QAAA,CAAA;AAA+B,QAChC,IAAA,EAAA,CAAA;AAAA,QACH,CAAA,EAAA,GAAA,UAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,EAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACE,KAAA,CAAA;AAAkB,IACpB,MAAA,mBAAA,GAAA,MAAA;AAEA,MAAM,SAAA,CAAA,KAAsC,GAAA,KAAA,CAAA;AAC1C,KAAM,CAAA;AACN,IAAO,MAAA,MAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACL,MAAA,EAAA,aAAA,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MAAA,OACK;AAAK,QACV,IAAM;AAAsC,QAC5C,GAAU,EAAA,IAAA,CAAA,GAAA;AAAA,QACV,IAAU,EAAA,IAAA,CAAC,QAAW,CAAA,aAAe,EAAA,SAAA,CAAA;AAAA,QACrC,QAAe,EAAA,KAAA;AAAA,QACjB,QAAA,EAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,QACF,aAAA,EAAA,KAAA;AAEA,OAAM,CAAA;AACJ,KAAA,CAAA;AACA,IAAA,MAAA,SAAa,GAAK,CAAA,GAAA,KAAA;AAClB,MAAA,IAAA,EAAA,CAAA;AACA,MAAK,MAAA,IAAA,GAAA,GAAA,CAAA;AAA+B,MACtC,IAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAEA,MAAA,CAAA,EAAA,8BAAsD,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,EAAA,CAAA;AACpD,MAAA,IAAA,CAAA,WAAmC,EAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AAAA,KAAA,CAAA;AAE/B,IAAA,MAAA,uBAAoB,GAAA,MAAA;AAAA,MAAA,QACP,KAAA,CAAA,mBAAA;AACb,QAAM,KAAA,OAAA;AACN,UAAA,0BAA0B;AAC1B,QAAA,KAAA;AAAkC,UAChC,MAAO,YAAG,GAAA,eAA6B,CAAA,KAAA,CAAA,CAAA;AAAuB,UAChE,MAAA,iBAAA,GAAA,YAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA,CAAA;AACA,UAAO,MAAA,WAAA,GAAA,YAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,MAAA,IAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,UACT,OAAA,WAAA,CAAA;AAAA,SACA;AACE,QAAA;AAAQ,UACZ,OAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAI,MAAA,oBAAiB,GAAA,MAAA;AAErB,MAAA,IAAA,CAAA,SAAc,KAAwB;AACtC,QAAA;AAEA,MAAA,MAAM,+BAAkB,EAAA,CAAA;AACxB,MAAM,MAAA,IAAA,GAAA,EAAS,CAAS;AACxB,MAAA,MAAA,OAAA,GAAuB,EAAA,CAAA;AAEvB,MAAA,aAAkB,CAAA,CAAA,IAAA,KAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAChB,MAAA,cACS,CAAA,KAAS,GAAA,OAAA,CAAA;AAElB,MAAA,IAAA,KAAa,CAAA,MAAA,EAAA;AACb,QAAA,KAAA,CAAM,cAAiB,CAAA,eAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAEvB,QAAA,MAAe,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AACb,QAAA,MAAI,SAAoB,GAAA,IAAA,CAAA,MAAA,CAAA;AACtB,QAAA,IAAA,SAAU,EAAA;AAAA,UAAA,IACR,KAAK,CAAA,YAAA,EAAA;AAAA,YACL,IAAA,CAAA;AAAoB,cACpB,GAAU,EAAA,CAAA,CAAA;AAAA,cACV,IAAe,EAAA,CAAA,EAAA,EAAA,SAAA,CAAA,CAAA;AAAA,cAChB,QAAA,EAAA,KAAA;AAAA,cACI,aAAA,EAAA,IAAA;AACL,aAAK,CAAA,CAAA;AAAyC,WAChD,MAAA;AAAA,YACF,IAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACF;AAEA,SAAA;AAAoB,OACtB;AAEA,MAAA;AACE,KAAA,CAAA;AACA,IAAM,MAAA,oBAAuB,GAAA,MAAA;AAGzB,MAAI,IAAA,EAAA,EAAA,EAAA,CAAK;AACT,MAAK,MAAA,EAAA,2BAAiC,EAAA,SAAA,EAAA,GAAA,KAAA,CAAA;AACtC,MAAO,MAAA,GAAA,GAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAmB,gBAAA,CAAA,KAAmB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QAC9C,IAAA,IAAA,CAAA,UAAA;AAEH,UAAI,YAAgB,CAAA;AAClB,QAAY,IAAA,CAAA,QAAA,CAAA,aAAe,EAAQ,SAAA,CAAA,CAAA;AACjC,QAAA,OAAe,YAAA,CAAA,IAAA,EAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OAAA,CACjB,CAAC;AACD,MAAe,IAAA,QAAA,CAAA,KAAA,EAAA;AACb,QAAA,WAAe,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAAA,UAChB,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA;AAAA,SACH,CAAA,CAAA;AAEA,QAAA,cAAkB,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA;AAClB,UAAA,GAAA,CAAA,QAAoB,GAAA,KAAA,CAAA;AACpB,SAAqB,CAAA,CAAA;AAAA,OACvB;AAEA,MAAA,sBAA6B,CAAA;AAC3B,MAAI,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAEJ,MAAI,oBAAmB,EAAA,CAAA;AACrB,KAAY,CAAA;AAA0B,IAAA,MACpC,cAAI,GAAa,MAAA;AAAkB,MACrC,IAAA,EAAA,CAAA;AAAA,MACF,IAAO,SAAA,CAAA;AACL,MAAY,IAAA,SAAA,CAAA,KAAA,IAAA,eAAwB,CAAI,KAAA,EAAA;AAAA,QAAA,SAClC,GAAA,eAAoB,CAAA,KAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAC1B,MAAA;AAAA,QACF,SAAA,GAAA,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAEA,OAAA;AACE,MAAA,IAAA,SAAgB,EAAA;AAChB,QAAC,SAAA,CAAA,KAAmB,EAAA,CAAA;AAAgB,QACtC,CAAA,SAAA,CAAA,KAAA,IAAA,SAAA,CAAA,KAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAM,MAAA,WAAA,GAAA;AACN,MAAA,IAAA,EAAM;AACN,MAAM,MAAA,UAAA,GAAA,CAAA,EAAA,GAAA,oBAA2C,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAEjD,MAAI,MAAa,YAAC,GAAY,UAAA,CAAA,KAAA,CAAA;AAE9B,MAAA,MAAuB,iBAAA,GAAA,CAAA,EAAA,GAAA,eAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AACrB,MAAA,IAAA,CAAA,uBAAyC;AAAA,QAAA,OACnC;AAA+B,MACrC,IAAA,iBAAA,EAAA;AACA,QAAA,MAAA,cAAqB,GAAA,iBAAc,CAAA,aAAsB,CAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAC3D,cAAA,CAAA,KAAA,CAAA,QAAA,GAAA,CAAA,EAAA,UAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAEA,OAAA;AACE,MAAM,IAAA;AAEN,QAAA,MAAM,EACJ,YAAA,EAAA,GAAA,YAAkB,CAAA;AAGpB,QAAA,MAAA,oBAA0B,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,EAAA,IAAA,CAAA,GAAA,CAAA,YAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,EAAA,CAAA,CAAA;AAC1B,QAAqB,UAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAAA;AAAA,QACvB,oBAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAM,CAAA;AACJ,IAAO,MAAA,eAAA,GAAA,CAAA,QAAwB,KAAA;AAAwB,MACzD,IAAA,EAAA,CAAA;AAEA,MAAM,OAAA,CAAA,EAAA,GAAA,gBAA+C,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,QAAA,CAAA,CAAA;AACnD,KAAqB,CAAA;AACrB,IAAA,MAAA,kBAA0B,GAAA,CAAA,KAAA,KAAA;AAAA,MAC5B,oBAAA,EAAA,CAAA;AAEA,MAAM,IAAA,CAAA,cAAA,EAAiB,KAAqB,CAAA,CAAA;AAC1C,KAAA,CAAA;AAEA,IAAA,MAAA,aAAgB,GAAA,CAAA,CAAA,KAAA;AAAA,MAAA,eACE,CAAA,KAAA;AAAA,QAChB,OAAgB;AACd,MAAoB,QAAA,CAAA,CAAA,IAAA;AACpB,QAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACF,KAAK,UAAW,CAAA,WAAA;AACd,UAAA,mBAAA,EAAwB,CAAA;AACxB,UAAA,MAAA;AACA,QAAA,KAAiB,UAAA,CAAA,IAAA;AACjB,UAAA,mBAAA,CAAA,IAAA,CAAA,CAAA;AAAA,kBACc,CAAA,cAAA,CAAA,CAAA;AACd,UAAI,CAAA,CAAA,cAAA,EAAc;AAChB,UAAA,MAAiB;AACjB,QAAA,KAAA,UAAkB,CAAA,GAAA;AAClB,UAAA,IAAA,aAAA,CAAA,KAAyB,KAAA,IAAA,EAAA;AAAA,YAC3B,CAAA,CAAA,cAAA,EAAA,CAAA;AACA,YAAA,CAAA,CAAA,eAAA,EAAA,CAAA;AAAA,+BACc,CAAA,KAAA,CAAA,CAAA;AACd,WAAA;AACA,UAAA,MAAA;AAAA,QACJ,KAAA,UAAA,CAAA,GAAA;AAAA,UACF,mBAAA,CAAA,KAAA,CAAA,CAAA;AAEA,UAAM;AACJ,OAAA;AACA,KAAA,CAAA;AACE,IAAqB,MAAA,WAAA,GAAA,MAAA;AAAA,MACvB,IAAA,EAAA,CAAA;AACA,MAAA,CAAA,EAAA,GAAA,gBAAyB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,EAAA,CAAA;AACzB,MAAA,IAAA,CAAK,aAAO,CAAA,KAAA,IAAA,KAAA,CAAA,UAAA,EAAA;AAAA,QACd,oBAAA,EAAA,CAAA;AAEA,OAAA;AACE,MAAM,mBAAY,CAAA,KAAA,CAAA,CAAA;AAClB,MAAA,IAAA,CAAA,OAAmB,CAAA,CAAA;AACnB,KAAA,CAAA;AAAyB,IAC3B,MAAA,oBAAA,GAAA,MAAA;AAEA,MAAM,MAAA,EAAA,KAAA,EAAA,GAAA,WAAyB,CAAuB;AACpD,MAAM,gBAAU,GAAI,KAAA,CAAA;AAEpB,MAAA,gBAAoB,CAAA,KAAA,GAAA,KAAA,CAAA;AAClB,KAAA,CAAA;AAA+D,IAAA,MAC1D,qBAAA,GAAA,CAAA,IAAA,KAAA;AACL,MAAA;AACA,MAAA,MAAA,EAAA,OAAA,EAAA,GAAA,IAAyB,CAAA;AAAA,MAC3B,IAAA,QAAA,CAAA,KAAA,EAAA;AAAA,QACF,CAAA,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AAEA,OAAM,MAAA;AACJ,QAAA,CAAA,YAAe,CAAE,EAAA,GAAA,gBAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACjB,QAAM,mBAAW,CAAA,KAAA,CAAA,CAAA;AAEjB,OAAA;AAAc,KAAA,CAAA;AACI,IAChB,MAAA,uBAAsB,GAAA,CAAA,CAAA,KAAA;AACpB,MAAA,MAAE,MAAe,GAAA,CAAA,CAAA,MAAA,CAAA;AACjB,MAAA,MAAA,EAAA,IAAiB,EAAA,GAAA,CAAA,CAAA;AACjB,MAAA,QAAA,IAAA;AAAA,QACE,KAAA,UAAA,CAAA,EAAA,CAAA;AAAA,QACE,KAAA,UAAA,CAAA,IAAA,EAAA;AAAA,UACA,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UAAA,MACI,QAAA,GAAA,IAAa,KAAA,UAAA,CAAA,EAAkB,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAAA,UACrC,SAAA,CAAA,UAAA,CAAA,MAAA,EAAA,QAAA,EAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA;AAAA,UACF,MAAA;AACA,SAAA;AAAA,QACF,KAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACA,KAAK,UAAW,CAAA,WAAA;AAAA,gBACA,CAAA,KAAA,EAAA,CAAA;AACd,UAAA,MAAA;AACA,OAAA;AAAA,KACJ,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,MAAA;AAEA,MAAA,wBAA2B,CAAA,KAAA,CAAA;AACzB,MAAA,MAAM,OAAO,GAAY,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACzB,MAAA,gBAAgB,GAAA,gBAAmB,CAAC,KAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,CAAA;AACpC,MAAmB,IAAA,CAAA,OAAA,IAAA,CAAA,gBAAA,IAAyB,KAAA,CAAA,YAAuB,IAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAEnE,QAAA,OAAgB;AACd,MAAA,IAAA,OAAA,CAAA,QAAA,EAAA;AAEF,QAAA,UAAY,OAAU,CAAA,CAAA;AACpB,OAAA,MAAA;AAAiB,QACZ,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA;AACL,OAAA;AAAmB,KACrB,CAAA;AAAA,IACF,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAEA,MAAM,MAAA,EAAA,GAAA,CAAA,CAAA,MAAe,CAAkB;AACrC,MAAA,MAAM,OAAO,UAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA;AACb,MAAM,IAAA,EAAA,CAAA,SAAkB,KAAA,IAAA,EAAgB;AACxC,QAAI,oBAAuB,IAAA,CAAA;AACzB,OAAA;AAAoB,MACtB,IAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AAAe,IACjB,MAAA,UAAA,GAAA,CAAA,CAAA,KAAA;AAEA,MAAM,WAAA,CAAA,KAAa,GAAmB,KAAA,CAAA;AACpC,MAAA,IAAA,CAAA,MAAA,EAAoB,CAAA,CAAA,CAAA;AACpB,KAAA,CAAA;AAAc,IAChB,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,MAAA,EAAA,KAAA,EAAA,GAAA,aAA8B,CAAA;AAClC,MAAM,IAAA,CAAA;AAEN,QAAA,OAAY;AAEZ,MAAM,MAAA,MAAA,GAAS,KAAM,CAAA,YAAA,CAAa,KAAK,CAAA,CAAA;AAEvC,MAAI,IAAA,SAAA,CAAU,MAAM,CAAG,EAAA;AACrB,QAAA,MAAA,CAAO,IAAK,CAAA,oBAAoB,CAAE,CAAA,KAAA,CAAM,MAAM;AAAA,SAE7C,CAAA,CAAA;AAAA,OACH,MAAA,IAAW,WAAW,KAAO,EAAA;AAC3B,QAAqB,oBAAA,EAAA,CAAA;AAAA,OAChB,MAAA;AACL,QAAoB,mBAAA,EAAA,CAAA;AAAA,OACtB;AAAA,KACF,EAAG,MAAM,QAAQ,CAAA,CAAA;AAEjB,IAAM,MAAA,WAAA,GAAc,CAAC,GAAA,EAAa,CAAsB,KAAA;AACtD,MAAC,CAAA,aAAA,CAAc,KAAS,IAAA,mBAAA,CAAoB,IAAI,CAAA,CAAA;AAEhD,MAAA,IAAI,KAAgB,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,WAAA;AAEpB,QAAM,OAAA;AAAqC,MAC7C,GAAA,GAAA,YAAA,EAAA,GAAA,mBAAA,EAAA,CAAA;AAEA,KAAM,CAAA;AACG,IAAA,yBACa,GAAA,CAAA,UAAyB,KAAA,iBAAe,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,CAAA,cAAA,CAAA,EAAA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAC5D,KAAI,CAAA,SAAA,EAAA,oBAAA,CAAA,CAAA;AAEN,IAAA,KAAA,CAAM,aAA+B,EAAA,UAAA,EAAA,MAAA,KAAA,CAAA,YAAA,CAAA,EAAA,oBAAA,CAAA,CAAA;AAErC,IAAA,KAAA,CAAA,WAAA,EAAA,MAAA;AAAA,MACE,QAAC,CAAA,MAAc,WAAY,EAAA,CAAA,CAAA;AAAwB,KACnD,CAAA,CAAA;AAAA,IACF,KAAA,CAAA,QAAA,EAAA,YAAA;AAEA,MAAA,iBAAmB;AACjB,MAAS,MAAA,wBAAmB,CAAA,KAAA,CAAA;AAAA,MAC7B,kBAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,IAAA,kBAAA,CAAA;AAED,MAAA,cAAgB;AACd,KAAA,CAAA,CAAA;AACA,IAAM,KAAA,CAAA,WAAA,EAAA,oBAA0B,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAChC,IAAqB,SAAA,CAAA,MAAA;AACrB,MAAY,MAAA,UAAA,GAAA,KAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,MACb,MAAA,gBAAA,GAAA,mBAAA,CAAA,UAAA,CAAA,CAAA;AAED,MAAA,kBAAmB,GAAA,UAAA,CAAA,YAAwB,IAAA,gBAAiB,CAAA;AAE5D,MAAA,iBAAgB,CAAA,UAAA,EAAA,WAAA,CAAA,CAAA;AACd,KAAM,CAAA,CAAA;AAEN,IAAM,MAAA,CAAA;AAEN,MAAA,eAAA;AACA,MAAA,gBAAA;AAAyC,MAC1C,mBAAA;AAED,MAAa,UAAA;AAAA,MAAA,WAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIX,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,SAAA,CAAA,EAAA;AAAA,QAAA,OAAA,EAAA,YAAA;AAAA,QAAA,GAAA,EAAA,UAAA;AAAA,QAAA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAIA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,cAAA,EAAA,CAAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAAA,gBAAA,EAAA,aAAA;AAAA,QAAA,qBAAA,EAAA,IAAA,CAAA,kBAAA;AAAA,QAIA,yBAAA,EAAA,KAAA;AAAA,QAAA,kBAAA,EAAA,KAAA;AAAA,QAAA,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,QAAA,UAAA,EAAA,CAAA,EAAAA,KAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAIA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAAA,MAAA,EAAA,mBAAA;AAAA,OAIA,EAAA;AAAA,QACD,OAAA,EAAAC,OAAA,CAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}