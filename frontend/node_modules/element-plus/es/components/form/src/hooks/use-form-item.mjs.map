{"version": 3, "file": "use-form-item.mjs", "sources": ["../../../../../../../packages/components/form/src/hooks/use-form-item.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  inject,\n  onMounted,\n  onUnmounted,\n  ref,\n  toRef,\n  watch,\n} from 'vue'\nimport { useId } from '@element-plus/hooks/use-id'\nimport { formContextKey, formItemContextKey } from '../constants'\n\nimport type { ComputedRef, Ref, WatchStopHandle } from 'vue'\nimport type { FormItemContext } from '../types'\n\nexport const useFormItem = () => {\n  const form = inject(formContextKey, undefined)\n  const formItem = inject(formItemContextKey, undefined)\n  return {\n    form,\n    formItem,\n  }\n}\n\nexport type IUseFormItemInputCommonProps = {\n  id?: string\n  label?: string | number | boolean | Record<string, any>\n  ariaLabel?: string | number | boolean | Record<string, any>\n}\n\nexport const useFormItemInputId = (\n  props: Partial<IUseFormItemInputCommonProps>,\n  {\n    formItemContext,\n    disableIdGeneration,\n    disableIdManagement,\n  }: {\n    formItemContext?: FormItemContext\n    disableIdGeneration?: ComputedRef<boolean> | Ref<boolean>\n    disableIdManagement?: ComputedRef<boolean> | Ref<boolean>\n  }\n) => {\n  if (!disableIdGeneration) {\n    disableIdGeneration = ref<boolean>(false)\n  }\n  if (!disableIdManagement) {\n    disableIdManagement = ref<boolean>(false)\n  }\n\n  const instance = getCurrentInstance()\n\n  const inLabel = () => {\n    let parent = instance?.parent\n    while (parent) {\n      if (parent.type.name === 'ElFormItem') {\n        return false\n      }\n      if (parent.type.name === 'ElLabelWrap') {\n        return true\n      }\n      parent = parent.parent\n    }\n    return false\n  }\n\n  const inputId = ref<string>()\n  let idUnwatch: WatchStopHandle | undefined = undefined\n\n  const isLabeledByFormItem = computed<boolean>(() => {\n    return !!(\n      !(props.label || props.ariaLabel) &&\n      formItemContext &&\n      formItemContext.inputIds &&\n      formItemContext.inputIds?.length <= 1\n    )\n  })\n\n  // Generate id for ElFormItem label if not provided as prop\n  onMounted(() => {\n    idUnwatch = watch(\n      [toRef(props, 'id'), disableIdGeneration] as any,\n      ([id, disableIdGeneration]: [string, boolean]) => {\n        const newId = id ?? (!disableIdGeneration ? useId().value : undefined)\n        if (newId !== inputId.value) {\n          if (formItemContext?.removeInputId && !inLabel()) {\n            inputId.value && formItemContext.removeInputId(inputId.value)\n            if (!disableIdManagement?.value && !disableIdGeneration && newId) {\n              formItemContext.addInputId(newId)\n            }\n          }\n          inputId.value = newId\n        }\n      },\n      { immediate: true }\n    )\n  })\n\n  onUnmounted(() => {\n    idUnwatch && idUnwatch()\n    if (formItemContext?.removeInputId) {\n      inputId.value && formItemContext.removeInputId(inputId.value)\n    }\n  })\n\n  return {\n    isLabeledByFormItem,\n    inputId,\n  }\n}\n"], "names": [], "mappings": ";;;;AAYY,MAAC,WAAW,GAAG,MAAM;AACjC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9C,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC;AACtD,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,kBAAkB,GAAG,CAAC,KAAK,EAAE;AAC1C,EAAE,eAAe;AACjB,EAAE,mBAAmB;AACrB,EAAE,mBAAmB;AACrB,CAAC,KAAK;AACN,EAAE,IAAI,CAAC,mBAAmB,EAAE;AAC5B,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,IAAI,CAAC,mBAAmB,EAAE;AAC5B,IAAI,mBAAmB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,MAAM;AACxB,IAAI,IAAI,MAAM,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC7D,IAAI,OAAO,MAAM,EAAE;AACnB,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;AAC7C,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AAC9C,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC;AACzB,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM;AAC7C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,GAAG,eAAe,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AACvK,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,SAAS,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,oBAAoB,CAAC,KAAK;AACjG,MAAM,MAAM,KAAK,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,oBAAoB,GAAG,KAAK,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACrF,MAAM,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,EAAE;AACnC,QAAQ,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,aAAa,KAAK,CAAC,OAAO,EAAE,EAAE;AAC9F,UAAU,OAAO,CAAC,KAAK,IAAI,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACxE,UAAU,IAAI,EAAE,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,IAAI,KAAK,EAAE;AACrH,YAAY,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAC9C,WAAW;AACX,SAAS;AACT,QAAQ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,OAAO;AACP,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5B,GAAG,CAAC,CAAC;AACL,EAAE,WAAW,CAAC,MAAM;AACpB,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;AAC7B,IAAI,IAAI,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC,aAAa,EAAE;AAC1E,MAAM,OAAO,CAAC,KAAK,IAAI,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpE,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,mBAAmB;AACvB,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}