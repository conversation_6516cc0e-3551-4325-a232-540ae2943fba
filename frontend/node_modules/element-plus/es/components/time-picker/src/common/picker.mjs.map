{"version": 3, "file": "picker.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/common/picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"refPopper\"\n    :visible=\"pickerVisible\"\n    effect=\"light\"\n    pure\n    trigger=\"click\"\n    v-bind=\"$attrs\"\n    role=\"dialog\"\n    teleported\n    :transition=\"`${nsDate.namespace.value}-zoom-in-top`\"\n    :popper-class=\"[`${nsDate.namespace.value}-picker__popper`, popperClass]\"\n    :popper-options=\"elPopperOptions\"\n    :fallback-placements=\"fallbackPlacements\"\n    :gpu-acceleration=\"false\"\n    :placement=\"placement\"\n    :stop-popper-mouse-event=\"false\"\n    :hide-after=\"0\"\n    persistent\n    @before-show=\"onBeforeShow\"\n    @show=\"onShow\"\n    @hide=\"onHide\"\n  >\n    <template #default>\n      <el-input\n        v-if=\"!isRangeInput\"\n        :id=\"(id as string | undefined)\"\n        ref=\"inputRef\"\n        container-role=\"combobox\"\n        :model-value=\"(displayValue as string)\"\n        :name=\"(name as string | undefined)\"\n        :size=\"pickerSize\"\n        :disabled=\"pickerDisabled\"\n        :placeholder=\"placeholder\"\n        :class=\"[nsDate.b('editor'), nsDate.bm('editor', type), $attrs.class]\"\n        :style=\"$attrs.style\"\n        :readonly=\"\n          !editable ||\n          readonly ||\n          isDatesPicker ||\n          isMonthsPicker ||\n          isYearsPicker ||\n          type === 'week'\n        \"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        :validate-event=\"false\"\n        @input=\"onUserInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @keydown=\"handleKeydownInput\"\n        @change=\"handleChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @click.stop\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"nsInput.e('icon')\"\n            @mousedown.prevent=\"onMouseDownInput\"\n            @touchstart.passive=\"onTouchStartInput\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"showClose && clearIcon\"\n            :class=\"`${nsInput.e('icon')} clear-icon`\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </el-input>\n      <picker-range-trigger\n        v-else\n        :id=\"(id as string[] | undefined)\"\n        ref=\"inputRef\"\n        :model-value=\"displayValue\"\n        :name=\"(name as string[] | undefined)\"\n        :disabled=\"pickerDisabled\"\n        :readonly=\"!editable || readonly\"\n        :start-placeholder=\"startPlaceholder\"\n        :end-placeholder=\"endPlaceholder\"\n        :class=\"rangeInputKls\"\n        :style=\"$attrs.style\"\n        :aria-label=\"ariaLabel\"\n        :tabindex=\"tabindex\"\n        autocomplete=\"off\"\n        role=\"combobox\"\n        @click=\"onMouseDownInput\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @start-input=\"handleStartInput\"\n        @start-change=\"handleStartChange\"\n        @end-input=\"handleEndInput\"\n        @end-change=\"handleEndChange\"\n        @mousedown=\"onMouseDownInput\"\n        @mouseenter=\"onMouseEnter\"\n        @mouseleave=\"onMouseLeave\"\n        @touchstart.passive=\"onTouchStartInput\"\n        @keydown=\"handleKeydownInput\"\n      >\n        <template #prefix>\n          <el-icon\n            v-if=\"triggerIcon\"\n            :class=\"[nsInput.e('icon'), nsRange.e('icon')]\"\n          >\n            <component :is=\"triggerIcon\" />\n          </el-icon>\n        </template>\n        <template #range-separator>\n          <slot name=\"range-separator\">\n            <span :class=\"nsRange.b('separator')\">{{ rangeSeparator }}</span>\n          </slot>\n        </template>\n        <template #suffix>\n          <el-icon\n            v-if=\"clearIcon\"\n            :class=\"clearIconKls\"\n            @mousedown.prevent=\"NOOP\"\n            @click=\"onClearIconClick\"\n          >\n            <component :is=\"clearIcon\" />\n          </el-icon>\n        </template>\n      </picker-range-trigger>\n    </template>\n    <template #content>\n      <slot\n        :visible=\"pickerVisible\"\n        :actual-visible=\"pickerActualVisible\"\n        :parsed-value=\"parsedValue\"\n        :format=\"format\"\n        :date-format=\"dateFormat\"\n        :time-format=\"timeFormat\"\n        :unlink-panels=\"unlinkPanels\"\n        :type=\"type\"\n        :default-value=\"defaultValue\"\n        :show-now=\"showNow\"\n        :show-footer=\"showFooter\"\n        :show-week-number=\"showWeekNumber\"\n        @pick=\"onPick\"\n        @select-range=\"setSelectionRange\"\n        @set-picker-option=\"onSetPickerOption\"\n        @calendar-change=\"onCalendarChange\"\n        @panel-change=\"onPanelChange\"\n        @mousedown.stop\n      />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  provide,\n  ref,\n  unref,\n  useAttrs,\n  watch,\n} from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { onClickOutside, unrefElement } from '@vueuse/core'\nimport {\n  useEmptyValues,\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport { useFormItem, useFormSize } from '@element-plus/components/form'\nimport ElInput from '@element-plus/components/input'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { NOOP, debugWarn, isArray } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { Calendar, Clock } from '@element-plus/icons-vue'\nimport { dayOrDaysToDate, formatter, parseDate, valueEquals } from '../utils'\nimport {\n  PICKER_BASE_INJECTION_KEY,\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n} from '../constants'\nimport { timePickerDefaultProps } from './props'\nimport PickerRangeTrigger from './picker-range-trigger.vue'\n\nimport type { InputInstance } from '@element-plus/components/input'\nimport type { Dayjs } from 'dayjs'\nimport type { ComponentPublicInstance, Ref } from 'vue'\nimport type { Options } from '@popperjs/core'\nimport type {\n  DateModelType,\n  DayOrDays,\n  PickerOptions,\n  SingleOrRange,\n  TimePickerDefaultProps,\n  UserInput,\n} from './props'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'Picker',\n})\n\nconst props = defineProps(timePickerDefaultProps)\nconst emit = defineEmits([\n  UPDATE_MODEL_EVENT,\n  CHANGE_EVENT,\n  'focus',\n  'blur',\n  'clear',\n  'calendar-change',\n  'panel-change',\n  'visible-change',\n  'keydown',\n])\nconst attrs = useAttrs()\n\nconst { lang } = useLocale()\n\nconst nsDate = useNamespace('date')\nconst nsInput = useNamespace('input')\nconst nsRange = useNamespace('range')\n\nconst { form, formItem } = useFormItem()\nconst elPopperOptions = inject(\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n  {} as Options\n)\nconst { valueOnClear } = useEmptyValues(props, null)\n\nconst refPopper = ref<TooltipInstance>()\nconst inputRef = ref<InputInstance>()\nconst pickerVisible = ref(false)\nconst pickerActualVisible = ref(false)\nconst valueOnOpen = ref<TimePickerDefaultProps['modelValue'] | null>(null)\nlet hasJustTabExitedInput = false\n\nconst pickerDisabled = computed(() => {\n  return props.disabled || !!form?.disabled\n})\n\nconst { isFocused, handleFocus, handleBlur } = useFocusController(inputRef, {\n  disabled: pickerDisabled,\n  beforeFocus() {\n    return props.readonly\n  },\n  afterFocus() {\n    pickerVisible.value = true\n  },\n  beforeBlur(event) {\n    return (\n      !hasJustTabExitedInput && refPopper.value?.isFocusInsideContent(event)\n    )\n  },\n  afterBlur() {\n    handleChange()\n    pickerVisible.value = false\n    hasJustTabExitedInput = false\n    props.validateEvent &&\n      formItem?.validate('blur').catch((err) => debugWarn(err))\n  },\n})\n\nconst rangeInputKls = computed(() => [\n  nsDate.b('editor'),\n  nsDate.bm('editor', props.type),\n  nsInput.e('wrapper'),\n  nsDate.is('disabled', pickerDisabled.value),\n  nsDate.is('active', pickerVisible.value),\n  nsRange.b('editor'),\n  pickerSize ? nsRange.bm('editor', pickerSize.value) : '',\n  attrs.class,\n])\n\nconst clearIconKls = computed(() => [\n  nsInput.e('icon'),\n  nsRange.e('close-icon'),\n  !showClose.value ? nsRange.e('close-icon--hidden') : '',\n])\n\nwatch(pickerVisible, (val) => {\n  if (!val) {\n    userInput.value = null\n    nextTick(() => {\n      emitChange(props.modelValue)\n    })\n  } else {\n    nextTick(() => {\n      if (val) {\n        valueOnOpen.value = props.modelValue\n      }\n    })\n  }\n})\nconst emitChange = (\n  val: TimePickerDefaultProps['modelValue'] | null,\n  isClear?: boolean\n) => {\n  // determine user real change only\n  if (isClear || !valueEquals(val, valueOnOpen.value)) {\n    emit(CHANGE_EVENT, val)\n    // Set the value of valueOnOpen when clearing to avoid triggering change events multiple times.\n    isClear && (valueOnOpen.value = val)\n    props.validateEvent &&\n      formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n}\nconst emitInput = (input: SingleOrRange<DateModelType> | null) => {\n  if (!valueEquals(props.modelValue, input)) {\n    let formatted\n    if (isArray(input)) {\n      formatted = input.map((item) =>\n        formatter(item, props.valueFormat, lang.value)\n      )\n    } else if (input) {\n      formatted = formatter(input, props.valueFormat, lang.value)\n    }\n    emit(UPDATE_MODEL_EVENT, input ? formatted : input, lang.value)\n  }\n}\nconst emitKeydown = (e: KeyboardEvent) => {\n  emit('keydown', e)\n}\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\n// @ts-ignore\nconst setSelectionRange = (start: number, end: number, pos?: 'min' | 'max') => {\n  const _inputs = refInput.value\n  if (!_inputs.length) return\n  if (!pos || pos === 'min') {\n    _inputs[0].setSelectionRange(start, end)\n    _inputs[0].focus()\n  } else if (pos === 'max') {\n    _inputs[1].setSelectionRange(start, end)\n    _inputs[1].focus()\n  }\n}\n\nconst onPick = (date: any = '', visible = false) => {\n  pickerVisible.value = visible\n  let result\n  if (isArray(date)) {\n    result = date.map((_) => _.toDate())\n  } else {\n    // clear btn emit null\n    result = date ? date.toDate() : date\n  }\n  userInput.value = null\n  emitInput(result)\n}\n\nconst onBeforeShow = () => {\n  pickerActualVisible.value = true\n}\n\nconst onShow = () => {\n  emit('visible-change', true)\n}\n\nconst onHide = () => {\n  pickerActualVisible.value = false\n  pickerVisible.value = false\n  emit('visible-change', false)\n}\n\nconst handleOpen = () => {\n  pickerVisible.value = true\n}\n\nconst handleClose = () => {\n  pickerVisible.value = false\n}\n\nconst parsedValue = computed(() => {\n  let dayOrDays: DayOrDays\n  if (valueIsEmpty.value) {\n    if (pickerOptions.value.getDefaultValue) {\n      dayOrDays = pickerOptions.value.getDefaultValue()\n    }\n  } else {\n    if (isArray(props.modelValue)) {\n      dayOrDays = props.modelValue.map((d) =>\n        parseDate(d, props.valueFormat, lang.value)\n      ) as [Dayjs, Dayjs]\n    } else {\n      dayOrDays = parseDate(\n        props.modelValue ?? '',\n        props.valueFormat,\n        lang.value\n      )!\n    }\n  }\n\n  if (pickerOptions.value.getRangeAvailableTime) {\n    const availableResult = pickerOptions.value.getRangeAvailableTime(\n      dayOrDays!\n    )\n    if (!isEqual(availableResult, dayOrDays!)) {\n      dayOrDays = availableResult\n\n      // The result is corrected only when model-value exists\n      if (!valueIsEmpty.value) {\n        emitInput(dayOrDaysToDate(dayOrDays))\n      }\n    }\n  }\n  if (isArray(dayOrDays!) && dayOrDays.some((day) => !day)) {\n    dayOrDays = [] as unknown as DayOrDays\n  }\n  return dayOrDays!\n})\n\nconst displayValue = computed<UserInput>(() => {\n  if (!pickerOptions.value.panelReady) return ''\n  const formattedValue = formatDayjsToString(parsedValue.value)\n  if (isArray(userInput.value)) {\n    return [\n      userInput.value[0] || (formattedValue && formattedValue[0]) || '',\n      userInput.value[1] || (formattedValue && formattedValue[1]) || '',\n    ]\n  } else if (userInput.value !== null) {\n    return userInput.value\n  }\n  if (!isTimePicker.value && valueIsEmpty.value) return ''\n  if (!pickerVisible.value && valueIsEmpty.value) return ''\n  if (formattedValue) {\n    return isDatesPicker.value || isMonthsPicker.value || isYearsPicker.value\n      ? (formattedValue as Array<string>).join(', ')\n      : formattedValue\n  }\n  return ''\n})\n\nconst isTimeLikePicker = computed(() => props.type.includes('time'))\n\nconst isTimePicker = computed(() => props.type.startsWith('time'))\n\nconst isDatesPicker = computed(() => props.type === 'dates')\n\nconst isMonthsPicker = computed(() => props.type === 'months')\n\nconst isYearsPicker = computed(() => props.type === 'years')\n\nconst triggerIcon = computed(\n  () => props.prefixIcon || (isTimeLikePicker.value ? Clock : Calendar)\n)\n\nconst showClose = ref(false)\n\nconst onClearIconClick = (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (showClose.value) {\n    event.stopPropagation()\n    // When the handleClear Function was provided, emit null will be executed inside it\n    // There is no need for us to execute emit null twice. #14752\n    if (pickerOptions.value.handleClear) {\n      pickerOptions.value.handleClear()\n    } else {\n      emitInput(valueOnClear.value)\n    }\n    emitChange(valueOnClear.value, true)\n    showClose.value = false\n    onHide()\n  }\n  emit('clear')\n}\n\nconst valueIsEmpty = computed(() => {\n  const { modelValue } = props\n  return (\n    !modelValue || (isArray(modelValue) && !modelValue.filter(Boolean).length)\n  )\n})\n\nconst onMouseDownInput = async (event: MouseEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if ((event.target as HTMLElement)?.tagName !== 'INPUT' || isFocused.value) {\n    pickerVisible.value = true\n  }\n}\nconst onMouseEnter = () => {\n  if (props.readonly || pickerDisabled.value) return\n  if (!valueIsEmpty.value && props.clearable) {\n    showClose.value = true\n  }\n}\nconst onMouseLeave = () => {\n  showClose.value = false\n}\n\nconst onTouchStartInput = (event: TouchEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n  if (\n    (event.touches[0].target as HTMLElement)?.tagName !== 'INPUT' ||\n    isFocused.value\n  ) {\n    pickerVisible.value = true\n  }\n}\n\nconst isRangeInput = computed(() => {\n  return props.type.includes('range')\n})\n\nconst pickerSize = useFormSize()\n\nconst popperEl = computed(() => unref(refPopper)?.popperRef?.contentRef)\n\nconst stophandle = onClickOutside(\n  inputRef as Ref<ComponentPublicInstance>,\n  (e: PointerEvent) => {\n    const unrefedPopperEl = unref(popperEl)\n    const inputEl = unrefElement(inputRef as Ref<ComponentPublicInstance>)\n    if (\n      (unrefedPopperEl &&\n        (e.target === unrefedPopperEl ||\n          e.composedPath().includes(unrefedPopperEl))) ||\n      e.target === inputEl ||\n      (inputEl && e.composedPath().includes(inputEl))\n    )\n      return\n    pickerVisible.value = false\n  }\n)\n\nonBeforeUnmount(() => {\n  stophandle?.()\n})\n\nconst userInput = ref<UserInput>(null)\n\nconst handleChange = () => {\n  if (userInput.value) {\n    const value = parseUserInputToDayjs(displayValue.value)\n    if (value) {\n      if (isValidValue(value)) {\n        emitInput(dayOrDaysToDate(value))\n        userInput.value = null\n      }\n    }\n  }\n  if (userInput.value === '') {\n    emitInput(valueOnClear.value)\n    emitChange(valueOnClear.value, true)\n    userInput.value = null\n  }\n}\n\nconst parseUserInputToDayjs = (value: UserInput) => {\n  if (!value) return null\n  return pickerOptions.value.parseUserInput!(value)\n}\n\nconst formatDayjsToString = (value: DayOrDays) => {\n  if (!value) return null\n  return pickerOptions.value.formatToString!(value)\n}\n\nconst isValidValue = (value: DayOrDays) => {\n  return pickerOptions.value.isValidValue!(value)\n}\n\nconst handleKeydownInput = async (event: Event | KeyboardEvent) => {\n  if (props.readonly || pickerDisabled.value) return\n\n  const { code } = event as KeyboardEvent\n  emitKeydown(event as KeyboardEvent)\n  if (code === EVENT_CODE.esc) {\n    if (pickerVisible.value === true) {\n      pickerVisible.value = false\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    return\n  }\n\n  if (code === EVENT_CODE.down) {\n    if (pickerOptions.value.handleFocusPicker) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n    if (pickerVisible.value === false) {\n      pickerVisible.value = true\n      await nextTick()\n    }\n    if (pickerOptions.value.handleFocusPicker) {\n      pickerOptions.value.handleFocusPicker()\n      return\n    }\n  }\n\n  if (code === EVENT_CODE.tab) {\n    hasJustTabExitedInput = true\n    return\n  }\n\n  if (code === EVENT_CODE.enter || code === EVENT_CODE.numpadEnter) {\n    if (\n      userInput.value === null ||\n      userInput.value === '' ||\n      isValidValue(parseUserInputToDayjs(displayValue.value) as DayOrDays)\n    ) {\n      handleChange()\n      pickerVisible.value = false\n    }\n    event.stopPropagation()\n    return\n  }\n\n  // if user is typing, do not let picker handle key input\n  if (userInput.value) {\n    event.stopPropagation()\n    return\n  }\n  if (pickerOptions.value.handleKeydownInput) {\n    pickerOptions.value.handleKeydownInput(event as KeyboardEvent)\n  }\n}\nconst onUserInput = (e: string) => {\n  userInput.value = e\n  // Temporary fix when the picker is dismissed and the input box\n  // is focused, just mimic the behavior of antdesign.\n  if (!pickerVisible.value) {\n    pickerVisible.value = true\n  }\n}\n\nconst handleStartInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [target.value, userInput.value[1]]\n  } else {\n    userInput.value = [target.value, null]\n  }\n}\n\nconst handleEndInput = (event: Event) => {\n  const target = event.target as HTMLInputElement\n  if (userInput.value) {\n    userInput.value = [userInput.value[0], target.value]\n  } else {\n    userInput.value = [null, target.value]\n  }\n}\n\nconst handleStartChange = () => {\n  const values = userInput.value as string[]\n  const value = parseUserInputToDayjs(values && values[0]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      formatDayjsToString(value) as string,\n      displayValue.value?.[1] || null,\n    ]\n    const newValue = [value, parsedVal && (parsedVal[1] || null)] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst handleEndChange = () => {\n  const values = unref(userInput) as string[]\n  const value = parseUserInputToDayjs(values && values[1]) as Dayjs\n  const parsedVal = unref(parsedValue) as [Dayjs, Dayjs]\n  if (value && value.isValid()) {\n    userInput.value = [\n      unref(displayValue)?.[0] || null,\n      formatDayjsToString(value) as string,\n    ]\n    const newValue = [parsedVal && parsedVal[0], value] as DayOrDays\n    if (isValidValue(newValue)) {\n      emitInput(dayOrDaysToDate(newValue))\n      userInput.value = null\n    }\n  }\n}\n\nconst pickerOptions = ref<Partial<PickerOptions>>({})\n// @ts-ignore\nconst onSetPickerOption = <T extends keyof PickerOptions>(\n  e: [T, PickerOptions[T]]\n) => {\n  pickerOptions.value[e[0]] = e[1]\n  pickerOptions.value.panelReady = true\n}\n\n// @ts-ignore\nconst onCalendarChange = (e: [Date, null | Date]) => {\n  emit('calendar-change', e)\n}\n\n// @ts-ignore\nconst onPanelChange = (\n  value: [Dayjs, Dayjs],\n  mode: 'month' | 'year',\n  view: unknown\n) => {\n  emit('panel-change', value, mode, view)\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nprovide(PICKER_BASE_INJECTION_KEY, {\n  props,\n})\n\ndefineExpose({\n  /**\n   * @description focus input box.\n   */\n  focus,\n  /**\n   * @description blur input box.\n   */\n  blur,\n  /**\n   * @description opens picker\n   */\n  handleOpen,\n  /**\n   * @description closes picker\n   */\n  handleClose,\n  /**\n   * @description pick item manually\n   */\n  onPick,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;mCAmNc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;AAcA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,SAAU,EAAA,CAAA;AAE3B,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AAEpC,IAAA,MAAM,EAAE,IAAA,EAAM,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AACvC,IAAA,MAAM,eAAkB,GAAA,MAAA,CAAA,mCAAA,EAAA,EAAA,CAAA,CAAA;AAAA,IACtB,MAAA,EAAA,YAAA,EAAA,GAAA,cAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IAAA,MACC,SAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACH,MAAA,QAAA,GAAA,GAAA,EAAA,CAAA;AACA,IAAA,MAAM,aAAE,GAAiB,GAAA,CAAA,KAAA,CAAA,CAAA;AAEzB,IAAA,MAAM,mBAAiC,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACvC,IAAA,MAAM,WAAW,GAAmB,GAAA,CAAA,IAAA,CAAA,CAAA;AACpC,IAAM,IAAA,wBAAyB,KAAA,CAAA;AAC/B,IAAM,MAAA,cAAA,GAAA,SAA0B,MAAK;AACrC,MAAM,OAAA,KAAA,CAAA,YAAmE,CAAA,EAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AACzE,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,EAAA,SAAA,EAAA,aAA0B,UAAM,EAAA,GAAA,kBAAA,CAAA,QAAA,EAAA;AACpC,MAAA,QAAa,EAAA,cAAA;AAAoB,MAClC,WAAA,GAAA;AAED,QAAA,OAAmB,KAAA,CAAA,QAAA,CAAA;AAAyD,OAChE;AAAA,MACV,UAAc,GAAA;AACZ,QAAA,aAAa,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACf;AAAA,MACA,UAAa,CAAA,KAAA,EAAA;AACX,QAAA,IAAA,EAAA,CAAA;AAAsB,QACxB,OAAA,CAAA,qBAAA,KAAA,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA;AAEE,MAAA,SACE,GAAC;AAAoE,QAEzE,YAAA,EAAA,CAAA;AAAA,QACY,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AACV,QAAa,qBAAA,GAAA,KAAA,CAAA;AACb,QAAA,KAAA,CAAA,aAAsB,KAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACtB,OAAwB;AACxB,KAAM,CAAA,CAAA;AACoD,IAC5D,MAAA,aAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,MAAA,CAAA,CAAA,CAAA,QAAA,CAAA;AAED,MAAM,MAAA,CAAA,EAAA,CAAA,QAAA,EAAgB,UAAe,CAAA;AAAA,MACnC,OAAO,EAAE,CAAQ,SAAA,CAAA;AAAA,MACjB,MAAO,CAAA,EAAA,CAAG,UAAU,EAAA,cAAU,CAAA,KAAA,CAAA;AAAA,MAC9B,MAAA,CAAA,GAAU,QAAS,EAAA,aAAA,CAAA,KAAA,CAAA;AAAA,MACnB,OAAO,CAAA,CAAA,CAAG,QAAY,CAAA;AAAoB,MAC1C,UAAU,GAAU,OAAA,CAAA,EAAA,CAAA,QAAA,EAAc,UAAK,CAAA,KAAA,CAAA,GAAA,EAAA;AAAA,MACvC,KAAA,CAAA,KAAkB;AAAA,KAAA,CAClB;AAAsD,IAAA,MAChD,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACP,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAED,MAAM,OAAA,CAAA,CAAA,CAAA;AAA8B,MAClC,CAAA,SAAU,CAAM,KAAA,GAAA,OAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,EAAA;AAAA,KAChB,CAAA,CAAA;AAAsB,IAAA,KACX,CAAA,aAAA,EAAQ,CAAQ,GAAA,KAAA;AAA0B,MACtD,IAAA,CAAA,GAAA,EAAA;AAED,QAAM,SAAA,CAAA,KAAA,GAAgB,IAAQ,CAAA;AAC5B,QAAA,QAAU,CAAA,MAAA;AACR,UAAA,UAAkB,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAClB,SAAA,CAAA,CAAA;AACE,OAAA,MAAA;AAA2B,QAC7B,QAAC,CAAA,MAAA;AAAA,UACI,IAAA,GAAA,EAAA;AACL,YAAA,WAAe,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AACb,WAAA;AACE,SAAA,CAAA,CAAA;AAA0B,OAC5B;AAAA,KAAA,CAAA,CAAA;AACD,IACH,MAAA,UAAA,GAAA,CAAA,GAAA,EAAA,OAAA,KAAA;AAAA,MACD,IAAA,OAAA,IAAA,CAAA,WAAA,CAAA,GAAA,EAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AACD,QAAM,IAAA,CAAA,YACJ,EAAA,GAEG,CAAA,CAAA;AAEH,QAAA,uBAAgB,CAAA,KAAiB,GAAA,GAAA,CAAA,CAAA;AAC/B,QAAA,KAAK,cAAc,KAAG,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEtB,OAAA;AACA,KAAM,CAAA;AACsD,IAC9D,MAAA,SAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,UAAA,EAAA,KAAA,CAAA,EAAA;AACA,QAAM,IAAA,SAAA,CAAY;AAChB,QAAA,IAAK,OAAA,CAAA,KAAkB,CAAA,EAAA;AACrB,UAAI,SAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA,SAAA,CAAA,IAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACJ,SAAI,MAAA,IAAA,OAAgB;AAClB,UAAA,SAAA,GAAY,SAAM,CAAA,KAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,SAAA;AAC6B,QAC/C,IAAA,CAAA,kBAAA,EAAA,KAAA,GAAA,SAAA,GAAA,KAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OAAA;AAEA,KAAA,CAAA;AAA0D,IAC5D,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AACA,MAAA,IAAA,CAAA,SAAyB,EAAA,CAAA,CAAA,CAAA;AAAqC,KAChE,CAAA;AAAA,IACF,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAM,IAAA,QAAA,CAAA,KAAc,EAAsB;AACxC,QAAA,iBAAiB,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAAA,OACnB;AAEA,MAAM,OAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,iBAAa,GAAA,CAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA;AAAA,MAAA,MACF,OAAA,GAAA,QAAU,CAAA,KAAA,CAAA;AAAwB,MAC7C,IAAA,CAAA,OAAA,CAAA,MAAA;AAAA,QACF,OAAA;AACA,MAAA,IAAA,CAAA,GAAQ,IAAA,GAAA,KAAA,KAAA,EAAA;AAAA,QACT,OAAA,CAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AAGD,QAAA,OAA0B,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA;AACxB,OAAA,kBAAyB,KAAA,EAAA;AACzB,QAAI,WAAS,iBAAQ,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACrB,QAAI,OAAQ,CAAA,CAAA,CAAA,CAAA,KAAA,EAAQ,CAAO;AACzB,OAAA;AACA,KAAQ,CAAA;AAAS,IACnB,MAAA,MAAA,WAA0B,EAAA,EAAA,OAAA,GAAA,KAAA,KAAA;AACxB,MAAA,aAAW,CAAkB,KAAA,GAAA,OAAA,CAAA;AAC7B,MAAQ,IAAA,MAAA,CAAC;AAAQ,MACnB,IAAA,OAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACF,MAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA;AAEA,OAAA,MAAe;AACb,QAAA,MAAA,GAAA,IAAsB,GAAA,IAAA,CAAA,MAAA,EAAA,GAAA,IAAA,CAAA;AACtB,OAAI;AACJ,MAAI,SAAA,CAAA,QAAe,IAAA,CAAA;AACjB,MAAA,SAAA,CAAA,MAAc,CAAI,CAAA;AAAiB,KAAA,CACrC;AAEE,IAAS,MAAA,YAAA,GAAY,MAAA;AAAW,MAClC,mBAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,MAAU,GAAM,MAAA;AAAA,MAClB,IAAA,CAAA,gBAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,MAAA,GAAA,MAAA;AAA4B,MAC9B,mBAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,MAAA,aAAe,CAAM,KAAA,GAAA,KAAA,CAAA;AACnB,MAAA,IAAA,CAAK,kBAAkB,KAAI,CAAA,CAAA;AAAA,KAC7B,CAAA;AAEA,IAAA,MAAM,UAAe,GAAA,MAAA;AACnB,MAAA,aAAA,CAAA,KAAA,GAA4B,IAAA,CAAA;AAC5B,KAAA,CAAA;AACA,IAAA,MAAA,oBAA4B;AAAA,MAC9B,aAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,WAAsB,GAAA,QAAA,CAAA,MAAA;AAAA,MACxB,IAAA,EAAA,CAAA;AAEA,MAAA,IAAM;AACJ,MAAA,IAAA,YAAsB,CAAA,KAAA,EAAA;AAAA,QACxB,IAAA,aAAA,CAAA,KAAA,CAAA,eAAA,EAAA;AAEA,UAAM,SAAA,GAAA,aAA6B,CAAA,KAAA,CAAA,eAAA,EAAA,CAAA;AACjC,SAAI;AACJ,OAAA;AACE,QAAI,IAAA,OAAA,CAAA,KAAA,CAAc,UAAuB,CAAA,EAAA;AACvC,UAAY,SAAA,GAAA,KAAA,CAAA,cAAoB,CAAgB,CAAA,CAAA,KAAA,SAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,SAClD,MAAA;AAAA,UACK,SAAA,GAAA,SAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,EAAA,KAAA,CAAA,WAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACL,SAAI;AACF,OAAA;AAA6B,MAAA,IAAA,aAC3B,CAAA,KAAU,sBAAsB;AAAU,QAC5C,MAAA,eAAA,GAAA,aAAA,CAAA,KAAA,CAAA,qBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QACF,IAAO,CAAA,OAAA,CAAA,eAAA,EAAA,SAAA,CAAA,EAAA;AACL,UAAY,SAAA,GAAA,eAAA,CAAA;AAAA,UAAA,iBACU,CAAA,KAAA,EAAA;AAAA,YACpB,SAAM,CAAA,eAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,WAAA;AACD,SACP;AAAA,OACF;AAAA,MACF,IAAA,OAAA,CAAA,SAAA,CAAA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AAEA,QAAI,SAAA,GAAA,EAAA,CAAA;AACF,OAAM;AAAsC,MAC1C,OAAA,SAAA,CAAA;AAAA,KACF,CAAA,CAAA;AACA,IAAA,MAAA,YAA8B,GAAA,QAAA,CAAA,MAAA;AAC5B,MAAY,IAAA,CAAA,aAAA,CAAA,KAAA,CAAA,UAAA;AAGZ,QAAI,OAAC;AACH,MAAU,MAAA,cAAA,GAAA,mBAA0B,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACtC,IAAA,OAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA;AAAA,QACF,OAAA;AAAA,UACF,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,cAAA,IAAA,cAAA,CAAA,CAAA,CAAA,IAAA,EAAA;AACA,UAAI,iBAAkB,CAAK,IAAA,kBAAgB,cAAe,CAAA,CAAA,CAAA,IAAA,EAAA;AACxD,SAAA,CAAA;AAAa,OACf,MAAA,IAAA,SAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AACA,QAAO,OAAA,SAAA,CAAA,KAAA,CAAA;AAAA,OACR;AAED,MAAM,IAAA,CAAA,YAAA,CAAA,qBAAyC,CAAA,KAAA;AAC7C,QAAA,OAAK,EAAA,CAAA;AACL,MAAM,IAAA,CAAA,aAAA,CAAA,KAAqC,IAAA,YAAA,CAAA,KAAA;AAC3C,QAAI,OAAA,EAAA,CAAQ;AACV,MAAO,IAAA,cAAA,EAAA;AAAA,QAAA,0BACoC,IAAA,cAAA,CAAA,KAAA,IAAA,aAAsB,CAAA,KAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,cAAA,CAAA;AAAA,OAAA;AACA,MACjE,OAAA,EAAA,CAAA;AAAA,KACF,CAAA,CAAA;AACE,IAAA,MAAA,gBAAiB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACnB,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAI,aAAC,GAAsB,QAAA,CAAA,MAAA,KAAa,UAAc,OAAA,CAAA,CAAA;AACtD,IAAA,MAAI,cAAC,GAAuB,QAAA,CAAA,MAAA,KAAa,UAAc,QAAA,CAAA,CAAA;AACvD,IAAA,MAAI,aAAgB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,OAAA,CAAA,CAAA;AAClB,IAAO,MAAA,WAAA,GAAA,eAAsC,KAAA,CAAA,UAAA,KAAA,gBAAuB,CAC/D,KAAA,GAAA,KAAA,GAAA,QAAsC,CAAA,CAAA,CAAA;AACvC,IACN,MAAA,SAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,gBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACR,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAED,QAAA;AAEA,MAAA,IAAM,eAAe,EAAS;AAE9B,QAAA,KAAsB,CAAA,eAAA,EAAA,CAAA;AAEtB,QAAA,IAAuB,aAAA,CAAA,KAAA,CAAA,WAAe,EAAA;AAEtC,UAAM,aAAgB,CAAA,KAAA,CAAA,WAAe,EAAA,CAAA;AAErC,SAAA,MAAoB;AAAA,UACZ,SAAqB,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAiC,SAC9D;AAEA,QAAM,UAAA,CAAA,YAAqB,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAE3B,QAAM,SAAA,CAAA,KAAA,GAAA,KAA0C,CAAA;AAC9C,QAAI,MAAA,EAAM,CAAY;AACtB,OAAA;AACE,MAAA,IAAA,CAAA,OAAsB,CAAA,CAAA;AAGtB,KAAI,CAAA;AACF,IAAA,MAAA,YAAA,WAAgC,CAAA,MAAA;AAAA,MAAA,MAC3B,EAAA,UAAA,EAAA,GAAA,KAAA,CAAA;AACL,MAAA,OAAA,CAAA,qBAA4B,CAAA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AACA,IAAW,MAAA,gBAAA,GAAA,YAAwB,KAAA;AACnC,MAAA,IAAA,EAAA,CAAA;AACA,MAAO,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,QACT,OAAA;AACA,MAAA,IAAA,CAAK,CAAO,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACd,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,OAAM;AACJ,KAAM,CAAA;AACN,IACE,MAAA,qBAAwB;AAA2C,MAEtE,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAED,QAAM,OAAA;AACJ,MAAI,IAAA,CAAA,YAAkB,CAAA,KAAA,IAAA,KAAA,CAAA,SAAsB,EAAA;AAC5C,QAAA,SAAW,CAAA,KAAA,GAAoC,IAAA,CAAA;AAC7C,OAAA;AAAsB,KACxB,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,MAAA;AACA,MAAA,uBAA2B,CAAA;AACzB,KAAI,CAAA;AACJ,IAAA,MAAI,iBAAuB,GAAA,CAAA,KAAA,KAAiB;AAC1C,MAAA,IAAA,EAAA,CAAA;AAAkB,MACpB,IAAA,KAAA,CAAA,QAAA,IAAA,cAAA,CAAA,KAAA;AAAA,QACF,OAAA;AACA,MAAA,IAAM,oBAAqB,CAAA,CAAA,CAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AACzB,QAAA,aAAkB,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACpB;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,YAAkB,GAAA,QAAA,CAAA,MAAA;AACtB,MACG,OAAA,WAAc,gBAAuC,CAAA,CAAA;AAGtD,KAAA,CAAA,CAAA;AAAsB,IACxB,MAAA,UAAA,GAAA,WAAA,EAAA,CAAA;AAAA,IACF,MAAA,QAAA,GAAA,QAAA,CAAA,MAAA;AAEA,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAO,OAAA,CAAA,EAAA,GAAM,CAAK,EAAA,GAAA,KAAA,CAAA,SAAgB,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,CAAA;AAAA,KACnC,CAAA,CAAA;AAED,IAAA,MAAM,aAAa,cAAY,CAAA,QAAA,EAAA,CAAA,CAAA,KAAA;AAE/B,MAAA,qBAA0B,GAAA,KAAA,CAAM,QAAe,CAAA,CAAA;AAE/C,MAAA,MAAmB,OAAA,GAAA,YAAA,CAAA,QAAA,CAAA,CAAA;AAAA,MACjB,IAAA,eAAA,KAAA,CAAA,CAAA,MAAA,KAAA,eAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA,OAAA,IAAA,OAAA,IAAA,CAAA,CAAA,YAAA,EAAA,CAAA,QAAA,CAAA,OAAA,CAAA;AAAA,QACqB,OAAA;AACnB,MAAM,aAAA,CAAA,KAAA,GAAA;AACN,KAAM,CAAA,CAAA;AACN,IAAA;AAOE,MAAA,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AACF,KAAA,CAAA,CAAA;AAAsB,IACxB,MAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,MAAA;AAEA,MAAA,IAAA,SAAA,CAAgB,KAAM,EAAA;AACpB,QAAa,MAAA,KAAA,GAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACd,IAAA,KAAA,EAAA;AAED,UAAM,IAAA,YAA2B,CAAI,KAAA,CAAA,EAAA;AAErC,qCAA2B,CAAA,KAAA,CAAA,CAAA,CAAA;AACzB,qBAAqB,CAAA,KAAA,GAAA,IAAA,CAAA;AACnB,WAAM;AACN,SAAA;AACE,OAAI;AACF,MAAU,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACV,QAAA,SAAA,CAAA,YAAkB,CAAA,KAAA,CAAA,CAAA;AAAA,QACpB,UAAA,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,QACF,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AACA,KAAI,CAAA;AACF,IAAA,MAAA,qBAAuB,GAAK,CAAA,KAAA,KAAA;AAC5B,MAAW,IAAA,CAAA,KAAA;AACX,QAAA,OAAA,IAAkB,CAAA;AAAA,MACpB,OAAA,aAAA,CAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,mBAAA,GAAA,CAAA,KAA8C,KAAA;AAClD,MAAI,IAAA,CAAC;AACL,QAAO,OAAA,IAAA,CAAA;AAAyC,MAClD,OAAA,aAAA,CAAA,KAAA,CAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,YAAe,GAAA,CAAA,KAAA,KAAA;AACnB,MAAO,OAAA,aAAA,CAAc,KAAM,CAAA,YAAA,CAAA,KAAqB,CAAA,CAAA;AAAA,KAClD,CAAA;AAEA,IAAM,MAAA,kBAAqC,GAAA,OAAA,KAAA,KAAA;AACzC,MAAO,IAAA,KAAA,CAAA,QAAA,IAAoB,cAAA,CAAA,KAAmB;AAAA,QAChD,OAAA;AAEA,MAAM,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AACJ,MAAI,WAAkB,CAAA,KAAA,CAAA,CAAA;AAEtB,MAAM,IAAA,IAAE,KAAK,UAAI,CAAA,GAAA,EAAA;AACjB,QAAA,IAAA,aAAkC,CAAA,KAAA,KAAA,IAAA,EAAA;AAClC,UAAI,sBAAyB,KAAA,CAAA;AAC3B,UAAI,KAAA,CAAA;AACF,UAAA,KAAA,CAAA,eAAsB,EAAA,CAAA;AACtB,SAAA;AACA,QAAA,OAAA;AAAsB,OACxB;AACA,MAAA,IAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA;AAAA,QACF,IAAA,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA;AAEA,UAAI,KAAA,CAAA,cAAoB,EAAM,CAAA;AAC5B,UAAI,KAAA,CAAA,iBAAuC,CAAA;AACzC,SAAA;AACA,QAAA,IAAA,aAAsB,CAAA,KAAA,KAAA,KAAA,EAAA;AAAA,UACxB,aAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACA,UAAI,MAAA,QAAA,EAAc;AAChB,SAAA;AACA,QAAA,IAAA,aAAe,CAAA,KAAA,CAAA,iBAAA,EAAA;AAAA,UACjB,aAAA,CAAA,KAAA,CAAA,iBAAA,EAAA,CAAA;AACA,UAAI,OAAA;AACF,SAAA;AACA,OAAA;AAAA,MACF,IAAA,IAAA,KAAA,UAAA,CAAA,GAAA,EAAA;AAAA,QACF,qBAAA,GAAA,IAAA,CAAA;AAEA,QAAI,OAAA;AACF,OAAwB;AACxB,MAAA,IAAA,IAAA,KAAA,UAAA,CAAA,KAAA,IAAA,IAAA,KAAA,UAAA,CAAA,WAAA,EAAA;AAAA,QACF,IAAA,SAAA,CAAA,KAAA,KAAA,IAAA,IAAA,SAAA,CAAA,KAAA,KAAA,EAAA,IAAA,YAAA,CAAA,qBAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AAEA,UAAI,YAAS,EAAA,CAAA;AACX,UACE,aAAoB,CAAA,KAAA,GAAA,KAAA,CAAA;AAIpB,SAAa;AACb,QAAA,KAAA,CAAA,eAAsB,EAAA,CAAA;AAAA,QACxB,OAAA;AACA,OAAA;AACA,MAAA,IAAA,SAAA,CAAA,KAAA,EAAA;AAAA,QACF,KAAA,CAAA,eAAA,EAAA,CAAA;AAGA,QAAA;AACE,OAAA;AACA,MAAA,IAAA,aAAA,CAAA,KAAA,CAAA,kBAAA,EAAA;AAAA,QACF,aAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,OAAI;AACF,KAAc,CAAA;AAA+C,IAC/D,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACF,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AACA,MAAM,IAAA,CAAA,aAAc,CAAC,KAAc,EAAA;AACjC,QAAA,aAAkB,CAAA,KAAA,GAAA,IAAA,CAAA;AAGlB,OAAI;AACF,KAAA,CAAA;AAAsB,IACxB,MAAA,gBAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,MAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAEA,MAAM,IAAA,SAAA,CAAA,KAAA,EAAA;AACJ,QAAA,eAAqB,GAAA,CAAA,MAAA,CAAA,KAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACrB,OAAA;AACE,QAAA,SAAA,CAAU,QAAQ,CAAC,MAAA,CAAO,OAAO,IAAU,CAAA,CAAA;AAAQ,OAC9C;AACL,KAAA,CAAA;AAAqC,IACvC,MAAA,cAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,MAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AAEA,MAAM,IAAA,SAAA,CAAA,KAAA,EAAiB;AACrB,QAAA,eAAqB,GAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACrB,OAAA;AACE,QAAA,SAAA,CAAU,QAAQ,CAAC,IAAA,EAAA,YAAiB,CAAA,CAAG;AAAY,OAC9C;AACL,KAAA,CAAA;AAAqC,IACvC,MAAA,iBAAA,GAAA,MAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAEA,MAAA,wBAA0B,CAAM,KAAA,CAAA;AAC9B,MAAA,MAAM,6BAAmB,CAAA,MAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACzB,MAAA,MAAM,SAAQ,GAAA,KAAA,CAAA,WAAA,CAAsB,CAAU;AAC9C,MAAM,IAAA,KAAA,IAAA,KAAY,QAAiB,EAAA,EAAA;AACnC,QAAI,SAAA,CAAA,KAAe,GAAA;AACjB,UAAA,mBAAkB,CAAA,KAAA,CAAA;AAAA,UAChB,oBAAoB,KAAK,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAAA,SACzB,CAAA;AAA2B,QAC7B,MAAA,QAAA,GAAA,CAAA,KAAA,EAAA,SAAA,KAAA,SAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA;AACA,QAAA,IAAA,aAAiB,QAAQ;AACzB,UAAI,SAAA,CAAA,eAAwB,CAAA,QAAA,CAAA,CAAA,CAAA;AAC1B,UAAU,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACV,SAAA;AAAkB,OACpB;AAAA,KACF,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,MAAA;AAEA,MAAA,IAAM;AACJ,MAAM,MAAA,MAAA,GAAS,MAAM,SAAS,CAAA,CAAA;AAC9B,MAAA,MAAM,KAAQ,GAAA,qBAAA,CAAsB,MAAU,IAAA,MAAA,CAAO,CAAC,CAAC,CAAA,CAAA;AACvD,MAAM,MAAA,SAAA,GAAY,MAAM,WAAW,CAAA,CAAA;AACnC,MAAI,IAAA,KAAA,IAAS,KAAM,CAAA,OAAA,EAAW,EAAA;AAC5B,QAAA,SAAA,CAAU,KAAQ,GAAA;AAAA,UAChB,CAAM,CAAA,EAAA,GAAA,KAAA,CAAA,YAAsB,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA;AAAA,UAC5B,oBAAoB,KAAK,CAAA;AAAA,SAC3B,CAAA;AACA,QAAA,MAAM,WAAW,CAAC,SAAA,IAAa,SAAU,CAAA,CAAC,GAAG,KAAK,CAAA,CAAA;AAClD,QAAI,IAAA,YAAA,CAAa,QAAQ,CAAG,EAAA;AAC1B,UAAU,SAAA,CAAA,eAAA,CAAgB,QAAQ,CAAC,CAAA,CAAA;AACnC,UAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAA;AAAA,SACpB;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgB,GAA4B,CAAA,EAAE,CAAA,CAAA;AAEpD,IAAM,MAAA,iBAAA,GAAoB,CACxB,CACG,KAAA;AACH,MAAA,aAAA,CAAc,MAAM,CAAE,CAAA,CAAC,CAAC,CAAA,GAAI,EAAE,CAAC,CAAA,CAAA;AAC/B,MAAA,aAAA,CAAc,MAAM,UAAa,GAAA,IAAA,CAAA;AAAA,KACnC,CAAA;AAGA,IAAM,MAAA,gBAAA,GAAmB,CAAC,CAA2B,KAAA;AACnD,MAAA,IAAA,CAAK,mBAAmB,CAAC,CAAA,CAAA;AAAA,KAC3B,CAAA;AAGA,IAAA,MAAM,aAAgB,GAAA,CACpB,KACA,EAAA,IAAA,EACA,IACG,KAAA;AACH,MAAK,IAAA,CAAA,cAAA,EAAgB,KAAO,EAAA,IAAA,EAAM,IAAI,CAAA,CAAA;AAAA,KACxC,CAAA;AAEA,IAAA,MAAM,QAAQ,MAAM;AAClB,MAAA,IAAA,EAAA,CAAA;AAAsB,MACxB,CAAA,EAAA,GAAA,QAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,IAAA,SAAqB;AAAA,MACvB,IAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,GAAQ,QAA2B,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KACjC,CAAA;AAAA,IACF,OAAC,CAAA,yBAAA,EAAA;AAED,MAAa,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,MAIX,IAAA;AAAA,MAAA,UAAA;AAAA,MAAA,WAAA;AAAA,MAAA,MAAA;AAAA,KAIA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,SAAA,CAAA,EAAAC,UAAA,CAAA;AAAA,QAAA,OAAA,EAAA,WAAA;AAAA,QAIA,GAAA,EAAA,SAAA;AAAA,QAAA,OAAA,EAAA,aAAA,CAAA,KAAA;AAAA,QAAA,MAAA,EAAA,OAAA;AAAA,QAAA,IAAA,EAAA,EAAA;AAAA,QAIA,OAAA,EAAA,OAAA;AAAA,OAAA,EAAA,IAAA,CAAA,MAAA,EAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,UAAA,EAAA,EAAA;AAAA,QAIA,UAAA,EAAA,CAAA,EAAAD,KAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QACD,cAAA,EAAA,CAAA,CAAA,EAAAA,KAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,eAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}