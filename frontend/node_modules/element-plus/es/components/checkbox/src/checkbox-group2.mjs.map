{"version": 3, "file": "checkbox-group2.mjs", "sources": ["../../../../../../packages/components/checkbox/src/checkbox-group.vue"], "sourcesContent": ["<template>\n  <component\n    :is=\"tag\"\n    :id=\"groupId\"\n    :class=\"ns.b('group')\"\n    role=\"group\"\n    :aria-label=\"\n      !isLabeledByFormItem ? ariaLabel || 'checkbox-group' : undefined\n    \"\n    :aria-labelledby=\"isLabeledByFormItem ? formItem?.labelId : undefined\"\n  >\n    <slot />\n  </component>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, provide, toRefs, watch } from 'vue'\nimport { isEqual, pick } from 'lodash-unified'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { debugWarn } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { checkboxGroupEmits, checkboxGroupProps } from './checkbox-group'\nimport { checkboxGroupContextKey } from './constants'\n\nimport type { CheckboxGroupValueType } from './checkbox-group'\n\ndefineOptions({\n  name: 'ElCheckboxGroup',\n})\n\nconst props = defineProps(checkboxGroupProps)\nconst emit = defineEmits(checkboxGroupEmits)\nconst ns = useNamespace('checkbox')\n\nconst { formItem } = useFormItem()\nconst { inputId: groupId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst changeEvent = async (value: CheckboxGroupValueType) => {\n  emit(UPDATE_MODEL_EVENT, value)\n  await nextTick()\n  emit(CHANGE_EVENT, value)\n}\n\nconst modelValue = computed({\n  get() {\n    return props.modelValue\n  },\n  set(val: CheckboxGroupValueType) {\n    changeEvent(val)\n  },\n})\n\nprovide(checkboxGroupContextKey, {\n  ...pick(toRefs(props), [\n    'size',\n    'min',\n    'max',\n    'disabled',\n    'validateEvent',\n    'fill',\n    'textColor',\n  ]),\n  modelValue,\n  changeEvent,\n})\n\nwatch(\n  () => props.modelValue,\n  (newVal, oldValue) => {\n    if (props.validateEvent && !isEqual(newVal, oldValue)) {\n      formItem?.validate('change').catch((err) => debugWarn(err))\n    }\n  }\n)\n</script>\n"], "names": ["_openBlock", "_createBlock", "_resolveDynamicComponent"], "mappings": ";;;;;;;;;;mCA2Bc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AACjC,IAAA,MAAM,EAAE,OAAS,EAAA,OAAA,EAAS,mBAAoB,EAAA,GAAI,mBAAmB,KAAO,EAAA;AAAA,MAC1E,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,OAAO,KAAkC,KAAA;AAC3D,MAAA,IAAA,CAAK,oBAAoB,KAAK,CAAA,CAAA;AAC9B,MAAA,MAAM,QAAS,EAAA,CAAA;AACf,MAAA,IAAA,CAAK,cAAc,KAAK,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,aAAa,QAAS,CAAA;AAAA,MAC1B,GAAM,GAAA;AACJ,QAAA,OAAO,KAAM,CAAA,UAAA,CAAA;AAAA,OACf;AAAA,MACA,IAAI,GAA6B,EAAA;AAC/B,QAAA,WAAA,CAAY,GAAG,CAAA,CAAA;AAAA,OACjB;AAAA,KACD,CAAA,CAAA;AAED,IAAA,OAAA,CAAQ,uBAAyB,EAAA;AAAA,MAC/B,GAAG,IAAA,CAAK,MAAO,CAAA,KAAK,CAAG,EAAA;AAAA,QACrB,MAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,UAAA;AAAA,QACA,eAAA;AAAA,QACA,MAAA;AAAA,QACA,WAAA;AAAA,OACD,CAAA;AAAA,MACD,UAAA;AAAA,MACA,WAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,MAAA,EAAA,QAAA,KAAA;AAAA,MACE,SAAY,CAAA,aAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,QAAA,CAAA,EAAA;AAAA,gBACU,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACpB,OAAA;AACE,KAAU,CAAA,CAAA;AAAgD,IAC5D,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,IAAA,EAAA,CAAA;AAAA,MACF,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,uBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;"}