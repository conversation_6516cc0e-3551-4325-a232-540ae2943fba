{"version": 3, "file": "card2.mjs", "sources": ["../../../../../../packages/components/card/src/card.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.is(`${shadow || globalConfig?.shadow || 'always'}-shadow`),\n    ]\"\n  >\n    <div v-if=\"$slots.header || header\" :class=\"[ns.e('header'), headerClass]\">\n      <slot name=\"header\">{{ header }}</slot>\n    </div>\n    <div :class=\"[ns.e('body'), bodyClass]\" :style=\"bodyStyle\">\n      <slot />\n    </div>\n    <div v-if=\"$slots.footer || footer\" :class=\"[ns.e('footer'), footerClass]\">\n      <slot name=\"footer\">{{ footer }}</slot>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { useGlobalConfig } from '@element-plus/components/config-provider'\nimport { cardProps } from './card'\n\nconst globalConfig = useGlobalConfig('card')\n\ndefineOptions({\n  name: 'ElCard',\n})\n\ndefineProps(cardProps)\n\nconst ns = useNamespace('card')\n</script>\n"], "names": [], "mappings": ";;;;;;mCA0Bc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;AAJA,IAAM,MAAA,YAAA,GAAe,gBAAgB,MAAM,CAAA,CAAA;AAQ3C,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}