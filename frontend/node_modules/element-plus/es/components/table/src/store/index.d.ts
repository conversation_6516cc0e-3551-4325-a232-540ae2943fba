import type { Ref } from 'vue';
import type { TableColumnCtx } from '../table-column/defaults';
import type { DefaultRow, Filter, Sort } from '../table/defaults';
interface WatcherPropsData<T extends DefaultRow> {
    data: Ref<T[]>;
    rowKey: Ref<string | null>;
}
declare function useStore<T extends DefaultRow>(): {
    mutations: {
        setData(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, data: T[]): void;
        insertColumn(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, column: TableColumnCtx<T>, parent: TableColumnCtx<T>, updateColumnOrder: () => void): void;
        updateColumnOrder(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, column: TableColumnCtx<T>): void;
        removeColumn(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, column: TableColumnCtx<T>, parent: TableColumnCtx<T>, updateColumnOrder: () => void): void;
        sort(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, options: Sort): void;
        changeSortCondition(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, options: Sort): void;
        filterChange(_states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, options: Filter<T>): void;
        toggleAllSelection(): void;
        rowSelectedChanged(_states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, row: T): void;
        setHoverRow(states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, row: T): void;
        setCurrentRow(_states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        }, row: T): void;
    };
    commit: (name: "sort" | "setData" | "insertColumn" | "updateColumnOrder" | "removeColumn" | "changeSortCondition" | "filterChange" | "toggleAllSelection" | "rowSelectedChanged" | "setHoverRow" | "setCurrentRow", ...args: any[]) => void;
    updateTableScrollY: () => void;
    assertRowKey: () => void;
    updateColumns: () => void;
    scheduleLayout: (needUpdateColumns?: boolean, immediate?: boolean) => void;
    isSelected: (row: T) => boolean;
    clearSelection: () => void;
    cleanSelection: () => void;
    getSelectionRows: () => T[];
    toggleRowSelection: (row: T, selected?: boolean, emitChange?: boolean, ignoreSelectable?: boolean) => void;
    _toggleAllSelection: () => void;
    toggleAllSelection: (() => void) | null;
    updateAllSelected: () => void;
    updateFilters: (column: TableColumnCtx<T>, values: string[]) => Record<string, string[]>;
    updateCurrentRow: (_currentRow: T) => void;
    updateSort: (column: TableColumnCtx<T> | null, prop: string | null, order: import("../table/defaults").TableSortOrder | null) => void;
    execFilter: () => void;
    execSort: () => void;
    execQuery: (ignore?: {
        filter: boolean;
    } | undefined) => void;
    clearFilter: (columnKeys?: string[] | string) => void;
    clearSort: () => void;
    toggleRowExpansion: (row: T, expanded?: boolean) => void;
    setExpandRowKeysAdapter: (val: string[]) => void;
    setCurrentRowKey: (key: string) => void;
    toggleRowExpansionAdapter: (row: T, expanded?: boolean) => void;
    isRowExpanded: (row: T) => boolean;
    updateExpandRows: () => void;
    updateCurrentRowData: () => void;
    loadOrToggle: (row: T) => void;
    updateTreeData: (ifChangeExpandRowKeys?: boolean, ifExpandAll?: boolean) => void;
    updateKeyChildren: (key: string, data: T[]) => void;
    states: {
        _currentRowKey: Ref<string | null>;
        currentRow: Ref<T | null>;
        expandRowKeys: Ref<string[]>;
        treeData: Ref<Record<string, import("./tree").TreeData>>;
        indent: Ref<number>;
        lazy: Ref<boolean>;
        lazyTreeNodeMap: Ref<Record<string, T[]>>;
        lazyColumnIdentifier: Ref<string>;
        childrenColumnName: Ref<string>;
        checkStrictly: Ref<boolean>;
        expandRows: Ref<T[]>;
        defaultExpandAll: Ref<boolean>;
        tableSize: Ref<any>;
        rowKey: Ref<string | null>;
        data: Ref<T[]>;
        _data: Ref<T[]>;
        isComplex: Ref<boolean>;
        _columns: Ref<TableColumnCtx<T>[]>;
        originColumns: Ref<TableColumnCtx<T>[]>;
        columns: Ref<TableColumnCtx<T>[]>;
        fixedColumns: Ref<TableColumnCtx<T>[]>;
        rightFixedColumns: Ref<TableColumnCtx<T>[]>;
        leafColumns: Ref<TableColumnCtx<T>[]>;
        fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
        rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
        updateOrderFns: (() => void)[];
        leafColumnsLength: Ref<number>;
        fixedLeafColumnsLength: Ref<number>;
        rightFixedLeafColumnsLength: Ref<number>;
        isAllSelected: Ref<boolean>;
        selection: Ref<T[]>;
        reserveSelection: Ref<boolean>;
        selectOnIndeterminate: Ref<boolean>;
        selectable: Ref<((row: T, index: number) => boolean) | null>;
        filters: Ref<StoreFilter>;
        filteredData: Ref<T[] | null>;
        sortingColumn: Ref<TableColumnCtx<T> | null>;
        sortProp: Ref<string | null>;
        sortOrder: Ref<string | number | null>;
        hoverRow: Ref<T | null>;
    };
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
};
export default useStore;
declare class HelperStore<T extends DefaultRow> {
    Return: {
        mutations: {
            setData(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, data: T[]): void;
            insertColumn(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, column: TableColumnCtx<T>, parent: TableColumnCtx<T>, updateColumnOrder: () => void): void;
            updateColumnOrder(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, column: TableColumnCtx<T>): void;
            removeColumn(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, column: TableColumnCtx<T>, parent: TableColumnCtx<T>, updateColumnOrder: () => void): void;
            sort(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, options: Sort): void;
            changeSortCondition(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, options: Sort): void;
            filterChange(_states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, options: Filter<T>): void;
            toggleAllSelection(): void;
            rowSelectedChanged(_states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, row: T): void;
            setHoverRow(states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, row: T): void;
            setCurrentRow(_states: {
                _currentRowKey: Ref<string | null>;
                currentRow: Ref<T | null>;
                expandRowKeys: Ref<string[]>;
                treeData: Ref<Record<string, import("./tree").TreeData>>;
                indent: Ref<number>;
                lazy: Ref<boolean>;
                lazyTreeNodeMap: Ref<Record<string, T[]>>;
                lazyColumnIdentifier: Ref<string>;
                childrenColumnName: Ref<string>;
                checkStrictly: Ref<boolean>;
                expandRows: Ref<T[]>;
                defaultExpandAll: Ref<boolean>;
                tableSize: Ref<any>;
                rowKey: Ref<string | null>;
                data: Ref<T[]>;
                _data: Ref<T[]>;
                isComplex: Ref<boolean>;
                _columns: Ref<TableColumnCtx<T>[]>;
                originColumns: Ref<TableColumnCtx<T>[]>;
                columns: Ref<TableColumnCtx<T>[]>;
                fixedColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedColumns: Ref<TableColumnCtx<T>[]>;
                leafColumns: Ref<TableColumnCtx<T>[]>;
                fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
                updateOrderFns: (() => void)[];
                leafColumnsLength: Ref<number>;
                fixedLeafColumnsLength: Ref<number>;
                rightFixedLeafColumnsLength: Ref<number>;
                isAllSelected: Ref<boolean>;
                selection: Ref<T[]>;
                reserveSelection: Ref<boolean>;
                selectOnIndeterminate: Ref<boolean>;
                selectable: Ref<((row: T, index: number) => boolean) | null>;
                filters: Ref<StoreFilter>;
                filteredData: Ref<T[] | null>;
                sortingColumn: Ref<TableColumnCtx<T> | null>;
                sortProp: Ref<string | null>;
                sortOrder: Ref<string | number | null>;
                hoverRow: Ref<T | null>;
            }, row: T): void;
        };
        commit: (name: "sort" | "setData" | "insertColumn" | "updateColumnOrder" | "removeColumn" | "changeSortCondition" | "filterChange" | "toggleAllSelection" | "rowSelectedChanged" | "setHoverRow" | "setCurrentRow", ...args: any[]) => void;
        updateTableScrollY: () => void;
        assertRowKey: () => void;
        updateColumns: () => void;
        scheduleLayout: (needUpdateColumns?: boolean, immediate?: boolean) => void;
        isSelected: (row: T) => boolean;
        clearSelection: () => void;
        cleanSelection: () => void;
        getSelectionRows: () => T[];
        toggleRowSelection: (row: T, selected?: boolean, emitChange?: boolean, ignoreSelectable?: boolean) => void;
        _toggleAllSelection: () => void;
        toggleAllSelection: (() => void) | null;
        updateAllSelected: () => void;
        updateFilters: (column: TableColumnCtx<T>, values: string[]) => Record<string, string[]>;
        updateCurrentRow: (_currentRow: T) => void;
        updateSort: (column: TableColumnCtx<T> | null, prop: string | null, order: import("../table/defaults").TableSortOrder | null) => void;
        execFilter: () => void;
        execSort: () => void;
        execQuery: (ignore?: {
            filter: boolean;
        } | undefined) => void;
        clearFilter: (columnKeys?: string[] | string) => void;
        clearSort: () => void;
        toggleRowExpansion: (row: T, expanded?: boolean) => void;
        setExpandRowKeysAdapter: (val: string[]) => void;
        setCurrentRowKey: (key: string) => void;
        toggleRowExpansionAdapter: (row: T, expanded?: boolean) => void;
        isRowExpanded: (row: T) => boolean;
        updateExpandRows: () => void;
        updateCurrentRowData: () => void;
        loadOrToggle: (row: T) => void;
        updateTreeData: (ifChangeExpandRowKeys?: boolean, ifExpandAll?: boolean) => void;
        updateKeyChildren: (key: string, data: T[]) => void;
        states: {
            _currentRowKey: Ref<string | null>;
            currentRow: Ref<T | null>;
            expandRowKeys: Ref<string[]>;
            treeData: Ref<Record<string, import("./tree").TreeData>>;
            indent: Ref<number>;
            lazy: Ref<boolean>;
            lazyTreeNodeMap: Ref<Record<string, T[]>>;
            lazyColumnIdentifier: Ref<string>;
            childrenColumnName: Ref<string>;
            checkStrictly: Ref<boolean>;
            expandRows: Ref<T[]>;
            defaultExpandAll: Ref<boolean>;
            tableSize: Ref<any>;
            rowKey: Ref<string | null>;
            data: Ref<T[]>;
            _data: Ref<T[]>;
            isComplex: Ref<boolean>;
            _columns: Ref<TableColumnCtx<T>[]>;
            originColumns: Ref<TableColumnCtx<T>[]>;
            columns: Ref<TableColumnCtx<T>[]>;
            fixedColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedColumns: Ref<TableColumnCtx<T>[]>;
            leafColumns: Ref<TableColumnCtx<T>[]>;
            fixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            rightFixedLeafColumns: Ref<TableColumnCtx<T>[]>;
            updateOrderFns: (() => void)[];
            leafColumnsLength: Ref<number>;
            fixedLeafColumnsLength: Ref<number>;
            rightFixedLeafColumnsLength: Ref<number>;
            isAllSelected: Ref<boolean>;
            selection: Ref<T[]>;
            reserveSelection: Ref<boolean>;
            selectOnIndeterminate: Ref<boolean>;
            selectable: Ref<((row: T, index: number) => boolean) | null>;
            filters: Ref<StoreFilter>;
            filteredData: Ref<T[] | null>;
            sortingColumn: Ref<TableColumnCtx<T> | null>;
            sortProp: Ref<string | null>;
            sortOrder: Ref<string | number | null>;
            hoverRow: Ref<T | null>;
        };
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string) => string;
            m: (modifier?: string) => string;
            be: (blockSuffix?: string, element?: string) => string;
            em: (element?: string, modifier?: string) => string;
            bm: (blockSuffix?: string, modifier?: string) => string;
            bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
    };
}
type StoreFilter = Record<string, string[]>;
type Store<T extends DefaultRow> = HelperStore<T>['Return'];
export type { WatcherPropsData, Store, StoreFilter };
