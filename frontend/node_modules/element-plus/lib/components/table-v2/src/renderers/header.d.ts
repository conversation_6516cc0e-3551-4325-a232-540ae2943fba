import type { FunctionalComponent } from 'vue';
import type { UseNamespaceReturn } from 'element-plus/es/hooks';
import type { TableV2HeaderRendererParams } from '../components';
import type { TableV2Props } from '../table';
type HeaderRendererProps = TableV2HeaderRendererParams & Pick<TableV2Props, 'headerClass' | 'headerProps'> & {
    ns: UseNamespaceReturn;
};
declare const HeaderRenderer: FunctionalComponent<HeaderRendererProps>;
export default HeaderRenderer;
